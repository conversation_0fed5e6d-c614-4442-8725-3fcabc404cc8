
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from '@/components/ui/tabs';
import { Lightbulb, Timer as TimerIcon, Shuffle } from 'lucide-react';
import Timer from './Timer';

const prompts = [
  "Describe un personaje que descubre un secreto familiar de generaciones.",
  "Escribe sobre un lugar donde el tiempo funciona diferente.",
  "Una carta misteriosa llega a la dirección incorrecta, pero contiene información vital.",
  "Dos extraños coinciden repetidamente a lo largo de un día.",
  "Un objeto común adquiere propiedades mágicas.",
  "Una tradición antigua cobra un significado inesperado.",
  "Alguien hereda una casa llena de cosas extrañas.",
  "Un personaje puede ver un aspecto del futuro, pero con limitaciones.",
  "Describe el primer contacto entre dos civilizaciones muy diferentes.",
  "Un viaje rutinario toma un giro inesperado."
];

const IdeasModule = () => {
  const [currentPrompt, setCurrentPrompt] = useState('');
  const [freeWritingText, setFreeWritingText] = useState('');
  const [isWritingComplete, setIsWritingComplete] = useState(false);
  const [wordCount, setWordCount] = useState(0);

  const generateRandomPrompt = () => {
    const randomIndex = Math.floor(Math.random() * prompts.length);
    setCurrentPrompt(prompts[randomIndex]);
  };

  const handleFreeWritingChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const text = e.target.value;
    setFreeWritingText(text);
    
    // Count words
    const words = text.trim() === '' ? 0 : text.trim().split(/\s+/).length;
    setWordCount(words);
  };

  const handleTimerEnd = () => {
    setIsWritingComplete(true);
  };

  const startNewSession = () => {
    setFreeWritingText('');
    setIsWritingComplete(false);
    setWordCount(0);
  };

  return (
    <Tabs defaultValue="prompts" className="w-full">
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="prompts">Prompts Creativos</TabsTrigger>
        <TabsTrigger value="freewriting">Escritura Libre</TabsTrigger>
      </TabsList>
      
      <TabsContent value="prompts" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Lightbulb className="mr-2 h-5 w-5 text-primary" />
              Generador de Prompts
            </CardTitle>
            <CardDescription>
              Genera ideas aleatorias para inspirar tu escritura.
            </CardDescription>
          </CardHeader>
          <CardContent>
            {currentPrompt ? (
              <div className="bg-secondary/50 p-6 rounded-lg border border-border">
                <p className="text-lg font-medium text-center">{currentPrompt}</p>
              </div>
            ) : (
              <div className="bg-secondary/50 p-6 rounded-lg border border-border flex items-center justify-center">
                <p className="text-muted-foreground text-center">
                  Haz clic en "Generar Prompt" para obtener una idea aleatoria.
                </p>
              </div>
            )}
          </CardContent>
          <CardFooter className="flex justify-center">
            <Button onClick={generateRandomPrompt} className="flex items-center">
              <Shuffle className="mr-2 h-4 w-4" />
              Generar Prompt
            </Button>
          </CardFooter>
        </Card>
      </TabsContent>
      
      <TabsContent value="freewriting" className="mt-4">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TimerIcon className="mr-2 h-5 w-5 text-primary" />
              Escritura Libre Cronometrada
            </CardTitle>
            <CardDescription>
              Escribe continuamente durante el tiempo establecido sin preocuparte por la edición.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="mb-6">
              <Timer initialMinutes={5} onTimerEnd={handleTimerEnd} />
            </div>
            
            <Textarea 
              placeholder="Comienza a escribir aquí..."
              className="min-h-[200px] resize-y"
              value={freeWritingText}
              onChange={handleFreeWritingChange}
              disabled={isWritingComplete}
            />
            
            <div className="mt-2 text-sm text-muted-foreground">
              {wordCount} palabras
            </div>
          </CardContent>
          <CardFooter className="flex justify-between">
            {isWritingComplete ? (
              <>
                <p className="text-sm font-medium text-green-600 dark:text-green-400">¡Tiempo completado!</p>
                <Button onClick={startNewSession}>Nueva Sesión</Button>
              </>
            ) : (
              <p className="text-sm text-muted-foreground">
                Continúa escribiendo hasta que termine el tiempo.
              </p>
            )}
          </CardFooter>
        </Card>
      </TabsContent>
    </Tabs>
  );
};

export default IdeasModule;
