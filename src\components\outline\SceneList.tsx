import React from 'react';
import { OutlineScene } from '@/types/outline';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DragDropContext, Droppable, Draggable, DropResult } from '@hello-pangea/dnd';
import { GripVertical, Edit, Trash, Plus, Calendar, Clock } from 'lucide-react';
import { format } from 'date-fns';

interface SceneListProps {
  scenes: OutlineScene[];
  onDragEnd: (result: DropResult) => void;
  onEditScene: (index: number) => void;
  onDeleteScene: (index: number) => void;
  onAddScene: () => void;
}

const SceneList: React.FC<SceneListProps> = ({
  scenes,
  onDragEnd,
  onEditScene,
  onDeleteScene,
  onAddScene
}) => {
  if (scenes.length === 0) {
    return (
      <Card className="border-dashed border-2 p-6 text-center">
        <p className="text-muted-foreground mb-4">No hay escenas en esta escaleta</p>
        <Button variant="secondary" onClick={onAddScene}>
          <Plus className="h-4 w-4 mr-2" />
          Añadir primera escena
        </Button>
      </Card>
    );
  }

  return (
    <DragDropContext onDragEnd={onDragEnd}>
      <Droppable droppableId="scenes">
        {(provided) => (
          <div
            {...provided.droppableProps}
            ref={provided.innerRef}
          >
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead className="w-8"></TableHead>
                    <TableHead className="w-[250px]">Título</TableHead>
                    <TableHead className="w-[200px]">Fecha/Hora</TableHead>
                    <TableHead>Descripción</TableHead>
                    <TableHead className="w-[100px] text-right">Acciones</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {scenes.map((scene, index) => (
                    <Draggable key={scene.id} draggableId={scene.id} index={index}>
                      {(provided) => (
                        <TableRow
                          ref={provided.innerRef}
                          {...provided.draggableProps}
                        >
                          <TableCell className="p-2">
                            <div
                              {...provided.dragHandleProps}
                              className="cursor-grab"
                            >
                              <GripVertical className="h-4 w-4 text-muted-foreground" />
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {scene.title}
                          </TableCell>
                          <TableCell>
                            {scene.sceneDate && (
                              <div className="flex items-center gap-2">
                                <Calendar className="h-4 w-4" />
                                {typeof scene.sceneDate === 'object' && scene.sceneDate instanceof Date && !isNaN(scene.sceneDate.getTime()) 
                                  ? format(scene.sceneDate, "dd/MM/yyyy")
                                  : 'Fecha inválida'}
                                {scene.sceneTime && (
                                  <>
                                    <Clock className="h-4 w-4 ml-2" />
                                    {scene.sceneTime}
                                  </>
                                )}
                              </div>
                            )}
                          </TableCell>
                          <TableCell className="truncate max-w-[300px]">
                            {scene.description}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end space-x-2">
                              <Button variant="ghost" size="icon" onClick={() => onEditScene(index)}>
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button variant="ghost" size="icon" onClick={() => onDeleteScene(index)}>
                                <Trash className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </Draggable>
                  ))}
                  {provided.placeholder}
                </TableBody>
              </Table>
            </Card>
          </div>
        )}
      </Droppable>
    </DragDropContext>
  );
};

export default SceneList;
