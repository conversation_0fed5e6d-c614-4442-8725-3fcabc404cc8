
import { useState } from "react";
import { <PERSON> } from "react-router-dom";
import { Menu, X, BookOpen, Moon, Sun, LogIn, FileText } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useTheme } from "@/components/ThemeProvider";
import { useFirebase } from "@/contexts/FirebaseContext";
import { preloadOnHover } from "@/utils/preload-components";

const Navbar = () => {
  const [isOpen, setIsOpen] = useState(false);
  const { theme, setTheme } = useTheme();
  const { currentUser } = useFirebase();

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const toggleTheme = () => {
    setTheme(theme === "dark" ? "light" : "dark");
  };

  const menuItems = [
    { name: "Inicio", path: "/" },
    { name: "Ideas", path: "/ideas" },
    { name: "Proyectos", path: "/projects" },
  ];

  return (
    <nav className="bg-background border-b border-border">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <BookOpen className="h-8 w-8 text-primary" />
              <span className="ml-2 text-xl font-bold font-heading">ScribeHub</span>
            </Link>
          </div>

          <div className="hidden md:block">
            <div className="ml-10 flex items-center space-x-4">
              {currentUser && menuItems.map((item) => (
                <Link
                  key={item.name}
                  to={item.path}
                  className="px-3 py-2 rounded-md text-sm font-medium hover:bg-secondary hover:text-foreground transition-colors"
                  onMouseEnter={() => preloadOnHover(item.path)}
                >
                  {item.name}
                </Link>
              ))}
              <Button variant="ghost" size="icon" onClick={toggleTheme}>
                {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
              </Button>
              <Link to="/auth">
                {currentUser ? (
                  <Button variant="outline" className="flex items-center space-x-2">
                    <span>{currentUser.email?.split('@')[0]}</span>
                  </Button>
                ) : (
                  <Button variant="secondary" className="flex items-center space-x-2">
                    <LogIn className="h-4 w-4 mr-1" />
                    <span>Acceder</span>
                  </Button>
                )}
              </Link>
            </div>
          </div>

          <div className="md:hidden">
            <Button variant="ghost" size="icon" onClick={toggleTheme} className="mr-2">
              {theme === "dark" ? <Sun className="h-5 w-5" /> : <Moon className="h-5 w-5" />}
            </Button>
            <Button variant="ghost" size="icon" onClick={toggleMenu}>
              {isOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
            </Button>
          </div>
        </div>
      </div>

      {isOpen && (
        <div className="md:hidden">
          <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3">
            {currentUser && menuItems.map((item) => (
              <Link
                key={item.name}
                to={item.path}
                className="block px-3 py-2 rounded-md text-base font-medium hover:bg-secondary hover:text-foreground"
                onClick={() => setIsOpen(false)}
              >
                {item.name}
              </Link>
            ))}
            <Link to="/auth" onClick={() => setIsOpen(false)}>
              {currentUser ? (
                <Button variant="outline" className="w-full mt-2">
                  {currentUser.email?.split('@')[0]}
                </Button>
              ) : (
                <Button variant="secondary" className="w-full mt-2 flex items-center justify-center">
                  <LogIn className="h-4 w-4 mr-1" />
                  <span>Acceder</span>
                </Button>
              )}
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navbar;
