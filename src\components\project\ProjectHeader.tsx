
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';
import { Project } from '@/types/project';

interface ProjectHeaderProps {
  project: Project;
}

const ProjectHeader = ({ project }: ProjectHeaderProps) => {
  const navigate = useNavigate();

  return (
    <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8">
      <div>
        <Button 
          variant="ghost" 
          onClick={() => navigate('/projects')}
          className="mb-2"
        >
          ← Volver a proyectos
        </Button>
        <h1 className="font-heading text-3xl font-bold mb-2">
          {project.title}
        </h1>
        {project.genre && (
          <div className="inline-block bg-secondary text-secondary-foreground px-2 py-1 rounded text-xs mb-2">
            {project.genre}
          </div>
        )}
        {project.description && (
          <p className="text-muted-foreground max-w-2xl">
            {project.description}
          </p>
        )}
      </div>
    </div>
  );
};

export default ProjectHeader;
