import { deleteProject } from './projects';
import { deleteAllProjectCharacters } from './characters';
import { deleteAllProjectChapters } from './chapters';
import { deleteAllProjectScenes } from './scenes';
import { deleteAllProjectLocations } from './locations';
import { deleteAllProjectPlotThreads } from './plot-threads';
import { deleteAllProjectWorldbuildingElements } from './worldbuilding';
import { deleteAllProjectResearchNotes } from './research-notes';
import { deleteAllProjectTimelines } from './timelines';
import { deleteAllProjectOutlines } from './outlines';

/**
 * Delete a project and all its subcollections
 * This is a utility function that orchestrates the deletion of all subcollections
 * and then deletes the project document itself.
 */
export const deleteProjectWithAllData = async (projectId: string): Promise<{ success: boolean }> => {
  try {
    // Delete all subcollections in parallel
    await Promise.all([
      deleteAllProjectCharacters(projectId),
      deleteAllProjectChapters(projectId),
      deleteAllProjectScenes(projectId),
      deleteAllProjectLocations(projectId),
      deleteAllProjectPlotThreads(projectId),
      deleteAllProjectWorldbuildingElements(projectId),
      deleteAllProjectResearchNotes(projectId),
      deleteAllProjectTimelines(projectId),
      deleteAllProjectOutlines(projectId)
    ]);

    // Finally delete the project document
    return await deleteProject(projectId);
  } catch (error) {
    console.error("Error deleting project with all data:", error);
    throw error;
  }
};
