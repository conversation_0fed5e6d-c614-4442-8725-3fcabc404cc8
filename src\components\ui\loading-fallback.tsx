import { Loader2 } from 'lucide-react';

interface LoadingFallbackProps {
  message?: string;
  className?: string;
}

export const LoadingFallback = ({ 
  message = "Cargando...", 
  className = "flex flex-col items-center justify-center min-h-[200px]" 
}: LoadingFallbackProps) => {
  return (
    <div className={className}>
      <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
};

export const PageLoadingFallback = ({ message = "Cargando página..." }: { message?: string }) => {
  return (
    <div className="container mx-auto py-8 px-4 flex flex-col items-center justify-center min-h-[60vh]">
      <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground text-lg">{message}</p>
    </div>
  );
};

export const ComponentLoadingFallback = ({ message = "Cargando componente..." }: { message?: string }) => {
  return (
    <div className="flex flex-col items-center justify-center p-8 min-h-[300px]">
      <Loader2 className="h-10 w-10 animate-spin text-primary mb-4" />
      <p className="text-muted-foreground">{message}</p>
    </div>
  );
};
