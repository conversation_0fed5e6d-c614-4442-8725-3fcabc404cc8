import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useFirebase } from '@/contexts/FirebaseContext';
import { Location } from '@/types/firestore-models';
import { Project } from '@/types/project';
import { getLocationById } from '@/lib/firebase/firestore/locations';
import { getProjectById } from '@/lib/firebase/firestore/projects';

interface UseLocationDetailProps {
  projectId: string | undefined;
  locationId: string | undefined;
}

export const useLocationDetail = ({ projectId, locationId }: UseLocationDetailProps) => {
  const { toast } = useToast();
  const { currentUser } = useFirebase();
  const navigate = useNavigate();

  const [location, setLocation] = useState<Location | null>(null);
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      if (!currentUser) {
        navigate('/auth');
        return;
      }

      if (!projectId || !locationId) {
        toast({
          title: "Error",
          description: "No se especificó un proyecto o localización para visualizar",
          variant: "destructive",
        });
        navigate('/projects');
        return;
      }

      try {
        setIsLoading(true);

        // Cargar el proyecto primero
        const projectData = await getProjectById(projectId);

        // Verificar que el proyecto pertenece al usuario actual
        if (projectData.userId !== currentUser.uid) {
          toast({
            title: "Acceso denegado",
            description: "No tienes permiso para ver este proyecto.",
            variant: "destructive",
          });
          navigate('/projects');
          return;
        }

        setProject(projectData);

        // Cargar la localización
        const locationData = await getLocationById(projectId, locationId);
        setLocation(locationData);
      } catch (error) {
        console.error("Error loading location details:", error);
        toast({
          title: "Error",
          description: "No se pudo cargar la información de la localización",
          variant: "destructive",
        });
        navigate(`/projects/${projectId}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [currentUser, projectId, locationId, navigate, toast]);

  return {
    location,
    project,
    isLoading,
    setLocation
  };
};
