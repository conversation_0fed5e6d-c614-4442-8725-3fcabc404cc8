
import { Suspense } from 'react';
import { Lightbulb } from 'lucide-react';
import { LazyIdeasModule } from './lazy-pages';
import { ComponentLoadingFallback } from '@/components/ui/loading-fallback';

const IdeasPage = () => {
  return (
    <div className="container mx-auto py-8 px-4">
      <div className="text-center mb-10">
        <h1 className="font-heading text-3xl font-bold mb-2 gradient-text inline-block">
          Generador de Ideas
        </h1>
        <p className="text-muted-foreground max-w-xl mx-auto">
          Encuentra inspiración para tu próxima obra utilizando diferentes herramientas creativas.
        </p>
      </div>

      <div className="max-w-2xl mx-auto">
        <Suspense fallback={<ComponentLoadingFallback message="Cargando herramientas creativas..." />}>
          <LazyIdeasModule />
        </Suspense>
      </div>
    </div>
  );
};

export default IdeasPage;
