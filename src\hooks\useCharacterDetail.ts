
import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { useToast } from '@/hooks/use-toast';
import { useFirebase } from '@/contexts/FirebaseContext';
import { getProjectById } from '@/lib/firebase/firestore/projects';
import { getCharacterById } from '@/lib/firebase/firestore/characters';
import { Character } from '@/types/firestore-models';
import { Project } from '@/types/project';

export const useCharacterDetail = (projectId?: string, characterId?: string) => {
  const [character, setCharacter] = useState<Character | null>(null);
  const [project, setProject] = useState<Project | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();
  const { currentUser } = useFirebase();
  const navigate = useNavigate();

  useEffect(() => {
    const loadData = async () => {
      if (!currentUser) {
        navigate('/auth');
        return;
      }

      if (!projectId || !characterId) {
        navigate('/projects');
        return;
      }

      try {
        setIsLoading(true);
        // First get the project to verify permissions
        const projectData = await getProjectById(projectId);

        if (projectData.userId !== currentUser.uid) {
          toast({
            title: "Acceso denegado",
            description: "No tienes permiso para ver este proyecto",
            variant: "destructive",
          });
          navigate('/projects');
          return;
        }

        // Then get the character using the new structure
        const characterData = await getCharacterById(projectId, characterId);
        setCharacter(characterData);

        setProject(projectData);
      } catch (error) {
        console.error("Error loading character data:", error);
        toast({
          title: "Error al cargar los datos",
          description: "No se pudo cargar la información del personaje",
          variant: "destructive",
        });
        navigate(`/projects/${projectId}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, [characterId, projectId, currentUser, navigate, toast]);

  return {
    character,
    project,
    isLoading,
    setCharacter
  };
};
