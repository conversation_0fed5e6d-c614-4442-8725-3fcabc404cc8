
import { useState, Suspense } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Users, Edit, Trash2, Eye, Plus } from 'lucide-react';
import { Character } from '@/types/firestore-models';
import { useNavigate } from 'react-router-dom';
import { LazyCharacterCreator } from '../../pages/lazy-pages';
import { ComponentLoadingFallback } from '@/components/ui/loading-fallback';
import QuickAddCharacter from '../character/QuickAddCharacter';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger
} from "@/components/ui/alert-dialog";

interface CharactersTabProps {
  characters: Character[];
  projectId: string;
  onCharacterUpdated: (character: Character) => void;
  onDeleteCharacter: (characterId: string) => void;
  isDeleting?: boolean;
}

const CharactersTab = ({
  characters,
  projectId,
  onCharacterUpdated,
  onDeleteCharacter,
  isDeleting = false
}: CharactersTabProps) => {
  const navigate = useNavigate();
  const [showCharacterCreator, setShowCharacterCreator] = useState(false);
  const [characterToEdit, setCharacterToEdit] = useState<Character | null>(null);
  const [characterToDelete, setCharacterToDelete] = useState<Character | null>(null);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);

  const handleDeleteCharacter = (character: Character) => {
    setCharacterToDelete(character);
    setIsDeleteDialogOpen(true);
  };

  const confirmDeleteCharacter = () => {
    if (characterToDelete && characterToDelete.id) {
      onDeleteCharacter(characterToDelete.id);
      setIsDeleteDialogOpen(false);
      setCharacterToDelete(null);
    }
  };

  return (
    <>
      {showCharacterCreator ? (
        <Card>
          <CardHeader>
            <CardTitle>
              {characterToEdit ? 'Editar personaje' : 'Crear nuevo personaje'}
            </CardTitle>
            <CardDescription>
              {characterToEdit ? 'Modifica los detalles del personaje' : 'Crea un personaje para tu proyecto'}
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Suspense fallback={<ComponentLoadingFallback message="Cargando editor de personaje..." />}>
              <LazyCharacterCreator
                editMode={!!characterToEdit}
                characterToEdit={characterToEdit || undefined}
                onCharacterUpdated={onCharacterUpdated}
                onCancel={() => {
                  setShowCharacterCreator(false);
                  setCharacterToEdit(null);
                }}
                projectId={projectId}
              />
            </Suspense>
          </CardContent>
        </Card>
      ) : (
        <>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-2xl font-bold">Personajes</h2>
            <div className="flex gap-2">
              <QuickAddCharacter
                projectId={projectId}
                onCharacterCreated={onCharacterUpdated}
                onOpenFullEditor={(character) => {
                  setCharacterToEdit(character);
                  setShowCharacterCreator(true);
                }}
              />
              <Button onClick={() => setShowCharacterCreator(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Nuevo personaje
              </Button>
            </div>
          </div>

          {characters.length === 0 ? (
            <div className="text-center py-12">
              <Users className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-medium mb-2">No hay personajes aún</h3>
              <p className="text-muted-foreground mb-6">
                Crea tu primer personaje para comenzar a desarrollar la historia
              </p>
              <div className="flex gap-2 justify-center">
                <QuickAddCharacter
                  projectId={projectId}
                  onCharacterCreated={onCharacterUpdated}
                  onOpenFullEditor={(character) => {
                    setCharacterToEdit(character);
                    setShowCharacterCreator(true);
                  }}
                />
                <Button onClick={() => setShowCharacterCreator(true)}>
                  <Plus className="h-4 w-4 mr-2" />
                  Crear personaje
                </Button>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {characters.map((character) => (
                <Card key={character.id} className="overflow-hidden">
                  <div className="aspect-[4/3] relative bg-secondary/30">
                    {character.profilePicture ? (
                      <img
                        src={character.profilePicture}
                        alt={character.name}
                        className="object-cover w-full h-full"
                      />
                    ) : (
                      <div className="flex items-center justify-center w-full h-full">
                        <Users className="h-12 w-12 text-muted-foreground" />
                      </div>
                    )}
                  </div>
                  <CardHeader className="pb-2">
                    <CardTitle>{character.name}</CardTitle>
                    <CardDescription>{character.occupation}</CardDescription>
                  </CardHeader>
                  <CardContent className="pb-2">
                    <p className="text-sm text-muted-foreground line-clamp-2">
                      {character.personality || "Sin descripción de personalidad"}
                    </p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setCharacterToEdit(character);
                        setShowCharacterCreator(true);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Editar
                    </Button>
                    <div className="flex gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => navigate(`/projects/${projectId}/characters/${character.id}`)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        Ver detalles
                      </Button>
                      <Button
                        variant="destructive"
                        size="sm"
                        onClick={() => handleDeleteCharacter(character)}
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Eliminar
                      </Button>
                    </div>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </>
      )}

      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
            <AlertDialogDescription>
              Esta acción no se puede deshacer. El personaje {characterToDelete?.name} será eliminado permanentemente.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancelar</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDeleteCharacter}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              Eliminar
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
};

export default CharactersTab;
