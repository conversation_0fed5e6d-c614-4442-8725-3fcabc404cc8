
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ArrowLeft, BookOpen } from 'lucide-react';
import { Outline } from '@/types/outline';

interface OutlineHeaderProps {
  outline: Outline;
  projectTitle: string;
  onBackClick: () => void;
  onConvertToScenes?: () => void;
}

const OutlineHeader: React.FC<OutlineHeaderProps> = ({
  outline,
  projectTitle,
  onBackClick,
  onConvertToScenes,
}) => {
  return (
    <div className="mb-6">
      <div className="flex justify-between items-center mb-4">
        <Button 
          variant="outline" 
          onClick={onBackClick}
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver a {projectTitle}
        </Button>
        
        {onConvertToScenes && (
          <Button 
            variant="secondary" 
            onClick={onConvertToScenes}
          >
            <BookOpen className="mr-2 h-4 w-4" />
            Convertir a escenas
          </Button>
        )}
      </div>
      
      <h1 className="text-3xl font-bold mb-2">{outline.title}</h1>
      {outline.description && (
        <p className="text-muted-foreground">{outline.description}</p>
      )}
    </div>
  );
};

export default OutlineHeader;
