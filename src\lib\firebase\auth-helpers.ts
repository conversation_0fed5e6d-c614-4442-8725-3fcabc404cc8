import { auth } from './config';
import { getIdToken, onAuthStateChanged } from 'firebase/auth';

/**
 * Ensures the current user is authenticated and returns their ID token
 * This can be useful for debugging authentication issues
 */
export const ensureAuthenticated = async (): Promise<string | null> => {
  const currentUser = auth.currentUser;

  if (!currentUser) {
    console.error("No authenticated user found");
    return null;
  }

  try {
    // Force token refresh to ensure we have the latest token
    const token = await getIdToken(currentUser, true);
    console.log("Authentication token refreshed successfully");
    return token;
  } catch (error) {
    console.error("Error getting authentication token:", error);
    return null;
  }
};

/**
 * Waits for the authentication state to be ready
 * This is useful when you need to ensure the auth state is fully initialized
 */
export const waitForAuthReady = (): Promise<void> => {
  return new Promise((resolve) => {
    const unsubscribe = onAuthStateChanged(auth, (user) => {
      unsubscribe();
      if (user) {
        console.log("Auth state ready, user is logged in:", user.uid);
      } else {
        console.log("Auth state ready, no user logged in");
      }
      resolve();
    });
  });
};

/**
 * Logs the current authentication state for debugging
 */
export const logAuthState = async () => {
  const currentUser = auth.currentUser;

  if (currentUser) {
    try {
      // Get the current token to check its validity
      const token = await getIdToken(currentUser, false);
      const tokenExpirationTime = JSON.parse(atob(token.split('.')[1])).exp * 1000;
      const expiresIn = new Date(tokenExpirationTime).toLocaleString();

      console.log("User is authenticated:", {
        uid: currentUser.uid,
        email: currentUser.email,
        emailVerified: currentUser.emailVerified,
        isAnonymous: currentUser.isAnonymous,
        tokenExpiration: expiresIn,
        tokenValid: Date.now() < tokenExpirationTime
      });

      return true;
    } catch (error) {
      console.error("Error checking auth token:", error);
      return false;
    }
  } else {
    console.log("No authenticated user found");
    return false;
  }
};
