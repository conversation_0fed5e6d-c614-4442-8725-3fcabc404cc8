import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Upload, Link as LinkIcon, Loader2, X } from 'lucide-react';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { validateImageUrl } from '@/components/character/ImageUrlValidator';

interface LocationImageUploaderProps {
  name: string;
  imageUrl?: string;
  onFileChange: (url: string) => void;
}

const LocationImageUploader = ({ name, imageUrl, onFileChange }: LocationImageUploaderProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [inputUrl, setInputUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('upload');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setError(null);

      // Create URL for preview
      const fileUrl = URL.createObjectURL(file);
      onFileChange(fileUrl);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputUrl(e.target.value);
    setError(null);
  };

  const handleLoadFromUrl = async () => {
    if (!inputUrl.trim()) {
      setError('Por favor, introduce una URL de imagen');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const isValid = await validateImageUrl(inputUrl);
      
      if (isValid) {
        onFileChange(inputUrl);
        setError(null);
      } else {
        setError('La URL no corresponde a una imagen válida');
      }
    } catch (err) {
      setError('Error al cargar la imagen. Verifica la URL e intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-4">
      <Label className="block mb-2">Imagen de la localización</Label>
      
      <div className="flex items-center gap-4 mb-4">
        {imageUrl ? (
          <div className="relative w-32 h-32 rounded-md overflow-hidden">
            <img 
              src={imageUrl} 
              alt={name || "Localización"} 
              className="w-full h-full object-cover"
            />
            <Button
              variant="destructive"
              size="icon"
              className="absolute top-1 right-1 h-6 w-6"
              onClick={() => onFileChange('')}
            >
              <X className="h-3 w-3" />
            </Button>
          </div>
        ) : (
          <div className="w-32 h-32 border-2 border-dashed rounded-md flex items-center justify-center bg-muted">
            <span className="text-muted-foreground text-sm text-center">
              Sin imagen
            </span>
          </div>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload">
            <Upload className="h-4 w-4 mr-2" />
            Archivo
          </TabsTrigger>
          <TabsTrigger value="url">
            <LinkIcon className="h-4 w-4 mr-2" />
            URL
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="upload" className="mt-2">
          <div className="text-center">
            <Input
              id="locationImage"
              type="file"
              accept="image/*"
              onChange={handleFileChange}
              className="sr-only"
            />
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => document.getElementById('locationImage')?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Seleccionar archivo
            </Button>
            {selectedFile && (
              <p className="text-xs mt-2 text-muted-foreground">
                {selectedFile.name}
              </p>
            )}
          </div>
        </TabsContent>
        
        <TabsContent value="url" className="mt-2 space-y-2">
          <div className="space-y-2">
            <Input
              type="url"
              placeholder="https://ejemplo.com/imagen.jpg"
              value={inputUrl}
              onChange={handleUrlChange}
            />
            <Button 
              className="w-full" 
              onClick={handleLoadFromUrl}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cargando...
                </>
              ) : (
                <>
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Cargar imagen
                </>
              )}
            </Button>
            {error && (
              <p className="text-xs text-destructive mt-1">{error}</p>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default LocationImageUploader;
