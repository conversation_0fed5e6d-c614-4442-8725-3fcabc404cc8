import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Loader2, FileText, Zap } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { createOutline } from '@/lib/firebase/firestore/outlines';
import { useFirebase } from '@/contexts/FirebaseContext';
import { Outline } from '@/types/outline';

interface QuickAddOutlineProps {
  projectId: string;
  onOutlineCreated: (outline: Outline) => void;
  onOpenFullEditor?: (outline: Outline) => void;
}

const QuickAddOutline = ({ projectId, onOutlineCreated, onOpenFullEditor }: QuickAddOutlineProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    title: '',
    type: '',
    quickNotes: ''
  });
  const { toast } = useToast();
  const { currentUser } = useFirebase();

  const outlineTypes = [
    'Capítulo',
    'Escena',
    'Acto',
    'Secuencia',
    'Episodio',
    'Parte',
    'Sección',
    'Otro'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      title: '',
      type: '',
      quickNotes: ''
    });
  };

  const handleSaveAndClose = async () => {
    if (!formData.title.trim()) {
      toast({
        title: "Título requerido",
        description: "Por favor, añade un título para la escaleta.",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser) {
      toast({
        title: "Error de autenticación",
        description: "Debes estar autenticado para crear escaletas.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const outlineData = {
        title: formData.title.trim(),
        type: formData.type || 'Escena',
        description: formData.quickNotes.trim(),
        // Campos por defecto para creación rápida
        scenes: [],
        notes: '',
        status: 'draft' as const,
        tags: [],
        wordCount: 0,
        estimatedDuration: 0,
        priority: 'medium' as const,
        lastModified: new Date(),
        version: 1,
        collaborators: [],
        isPublic: false,
        templateId: null,
        parentOutlineId: null,
        order: 0,
        metadata: {}
      };

      const newOutline = await createOutline(projectId, outlineData);
      
      onOutlineCreated(newOutline);
      resetForm();
      setIsOpen(false);

      toast({
        title: "Escaleta creada",
        description: `"${formData.title}" ha sido añadida al proyecto. Puedes editarla más tarde para añadir detalles.`,
      });
    } catch (error) {
      console.error("Error creating quick outline:", error);
      toast({
        title: "Error al crear escaleta",
        description: "No se pudo crear la escaleta. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAndEdit = async () => {
    if (!formData.title.trim()) {
      toast({
        title: "Título requerido",
        description: "Por favor, añade un título para la escaleta.",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser) {
      toast({
        title: "Error de autenticación",
        description: "Debes estar autenticado para crear escaletas.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const outlineData = {
        title: formData.title.trim(),
        type: formData.type || 'Escena',
        description: formData.quickNotes.trim(),
        // Campos por defecto
        scenes: [],
        notes: '',
        status: 'draft' as const,
        tags: [],
        wordCount: 0,
        estimatedDuration: 0,
        priority: 'medium' as const,
        lastModified: new Date(),
        version: 1,
        collaborators: [],
        isPublic: false,
        templateId: null,
        parentOutlineId: null,
        order: 0,
        metadata: {}
      };

      const newOutline = await createOutline(projectId, outlineData);
      
      onOutlineCreated(newOutline);
      resetForm();
      setIsOpen(false);

      // Abrir el editor completo
      onOpenFullEditor?.(newOutline);

      toast({
        title: "Escaleta creada",
        description: `"${formData.title}" ha sido creada. Ahora puedes añadir más detalles.`,
      });
    } catch (error) {
      console.error("Error creating quick outline:", error);
      toast({
        title: "Error al crear escaleta",
        description: "No se pudo crear la escaleta. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Zap className="h-4 w-4" />
          Añadir Rápido
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5 text-primary" />
            Crear Escaleta Rápida
          </DialogTitle>
          <DialogDescription>
            Captura la idea básica de tu escaleta. Podrás añadir escenas y detalles después.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="quick-title" className="text-sm font-medium">
              Título *
            </label>
            <Input
              id="quick-title"
              placeholder="Ej: Capítulo 1 - El encuentro"
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSaveAndClose();
                }
              }}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="quick-type" className="text-sm font-medium">
              Tipo de escaleta
            </label>
            <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona un tipo" />
              </SelectTrigger>
              <SelectContent>
                {outlineTypes.map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="quick-notes" className="text-sm font-medium">
              Descripción rápida
            </label>
            <Textarea
              id="quick-notes"
              placeholder="Idea central, eventos principales, o cualquier detalle que quieras recordar..."
              value={formData.quickNotes}
              onChange={(e) => handleInputChange('quickNotes', e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant="secondary"
            onClick={handleSaveAndEdit}
            disabled={isLoading || !formData.title.trim()}
            className="gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <FileText className="h-4 w-4" />
            )}
            Guardar y Editar
          </Button>
          <Button
            onClick={handleSaveAndClose}
            disabled={isLoading || !formData.title.trim()}
            className="gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Plus className="h-4 w-4" />
            )}
            Guardar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default QuickAddOutline;
