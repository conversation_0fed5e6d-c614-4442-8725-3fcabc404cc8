/**
 * Script para probar las reglas de Firestore
 * Ejecutar con: node test-firestore-rules.js
 * 
 * Nota: Este script requiere que el emulador de Firestore esté ejecutándose
 * Ejecutar: firebase emulators:start --only firestore
 */

const { initializeApp } = require('firebase/app');
const { getFirestore, connectFirestoreEmulator, doc, setDoc, getDoc, collection, addDoc } = require('firebase/firestore');
const { getAuth, connectAuthEmulator, signInAnonymously } = require('firebase/auth');

// Configuración de Firebase para el emulador
const firebaseConfig = {
  projectId: 'demo-project',
  authDomain: 'demo-project.firebaseapp.com',
};

const app = initializeApp(firebaseConfig);
const db = getFirestore(app);
const auth = getAuth(app);

// Conectar a los emuladores
connectFirestoreEmulator(db, 'localhost', 8080);
connectAuthEmulator(auth, 'http://localhost:9099');

async function testFirestoreRules() {
  console.log('🧪 Iniciando pruebas de reglas de Firestore...\n');

  try {
    // Autenticar usuario
    const userCredential = await signInAnonymously(auth);
    const user = userCredential.user;
    console.log('✅ Usuario autenticado:', user.uid);

    // Test 1: Crear proyecto válido
    console.log('\n📝 Test 1: Crear proyecto válido');
    try {
      const validProject = {
        title: 'Mi Proyecto de Prueba',
        logline: 'Una historia increíble',
        genre: 'Fantasía',
        userId: user.uid,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      const projectRef = await addDoc(collection(db, 'projects'), validProject);
      console.log('✅ Proyecto creado exitosamente:', projectRef.id);
    } catch (error) {
      console.log('❌ Error al crear proyecto válido:', error.message);
    }

    // Test 2: Intentar crear proyecto con userId diferente (debe fallar)
    console.log('\n📝 Test 2: Intentar crear proyecto con userId diferente');
    try {
      const invalidProject = {
        title: 'Proyecto Malicioso',
        logline: 'Intentando asignar a otro usuario',
        genre: 'Thriller',
        userId: 'otro-usuario-id', // ❌ Esto debe fallar
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await addDoc(collection(db, 'projects'), invalidProject);
      console.log('❌ ERROR: Se permitió crear proyecto con userId diferente');
    } catch (error) {
      console.log('✅ Correctamente bloqueado:', error.message);
    }

    // Test 3: Intentar crear proyecto sin título (debe fallar)
    console.log('\n📝 Test 3: Intentar crear proyecto sin título');
    try {
      const projectWithoutTitle = {
        title: '', // ❌ Título vacío debe fallar
        logline: 'Sin título',
        genre: 'Drama',
        userId: user.uid,
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await addDoc(collection(db, 'projects'), projectWithoutTitle);
      console.log('❌ ERROR: Se permitió crear proyecto sin título');
    } catch (error) {
      console.log('✅ Correctamente bloqueado:', error.message);
    }

    // Test 4: Intentar crear proyecto sin userId (debe fallar)
    console.log('\n📝 Test 4: Intentar crear proyecto sin userId');
    try {
      const projectWithoutUserId = {
        title: 'Proyecto sin userId',
        logline: 'Sin userId',
        genre: 'Comedia',
        // userId: user.uid, // ❌ Sin userId debe fallar
        createdAt: new Date(),
        updatedAt: new Date()
      };

      await addDoc(collection(db, 'projects'), projectWithoutUserId);
      console.log('❌ ERROR: Se permitió crear proyecto sin userId');
    } catch (error) {
      console.log('✅ Correctamente bloqueado:', error.message);
    }

    console.log('\n🎉 Pruebas completadas');

  } catch (error) {
    console.error('❌ Error en las pruebas:', error);
  }
}

// Ejecutar las pruebas
testFirestoreRules().catch(console.error);
