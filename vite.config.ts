import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => ({
  server: {
    host: "localhost",
    port: 3000,
    strictPort: false,
    fs: {
      // Habilitar la indexación del código para search_codebase
      allow: [
        // Permitir acceso a todos los archivos del proyecto
        path.resolve(__dirname),
      ],
    },
  },
  plugins: [
    react(),
    // Se ha eliminado la referencia a componentTagger
  ].filter(Boolean),
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
}));
