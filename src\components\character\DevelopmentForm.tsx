import { TextAreaField } from './FormFields';
import { Character } from '@/types/firestore-models';

interface DevelopmentFormProps {
  character: Character | Omit<Character, 'id'>;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const DevelopmentForm = ({ 
  character, 
  onInputChange
}: DevelopmentFormProps) => {
  return (
    <div className="space-y-6">
      <TextAreaField
        id="arc_summary"
        name="arc_summary"
        label="Resumen del arco narrativo"
        value={character.arc_summary || ''}
        onChange={onInputChange}
        placeholder="Describe cómo evoluciona el personaje a lo largo de la historia..."
        minHeight="100px"
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <TextAreaField
          id="initial_state"
          name="initial_state"
          label="Estado inicial"
          value={character.initial_state || ''}
          onChange={onInputChange}
          placeholder="¿Cómo empieza el personaje al inicio de la historia?"
          minHeight="120px"
        />
        
        <TextAreaField
          id="final_state"
          name="final_state"
          label="Estado final"
          value={character.final_state || ''}
          onChange={onInputChange}
          placeholder="¿Cómo se espera que termine o se transforme el personaje?"
          minHeight="120px"
        />
      </div>
      
      <div className="bg-secondary/30 p-4 rounded-md">
        <h3 className="text-sm font-medium mb-2">Consejos para un buen arco de personaje:</h3>
        <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
          <li>Define claramente el punto de partida del personaje (creencias, debilidades, fortalezas)</li>
          <li>Establece qué eventos o catalizadores provocarán su cambio</li>
          <li>Piensa en los obstáculos que deberá superar para transformarse</li>
          <li>Considera cómo las relaciones con otros personajes influyen en su desarrollo</li>
          <li>El cambio debe ser gradual y creíble, no repentino</li>
        </ul>
      </div>
    </div>
  );
};

export default DevelopmentForm;
