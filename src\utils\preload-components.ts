/**
 * Utility functions for preloading components to improve user experience
 */

// Import lazy components for preloading
import { 
  LazyCharacterCreator,
  LazyOutlineCreator,
  LazyIdeasModule,
  LazyCharactersTab,
  LazyLocationsTab,
  LazyOutlinesTab
} from '../pages/lazy-pages';

/**
 * Preload components that are likely to be used soon
 * This can be called on user interactions like hover or focus
 */
export const preloadComponent = (componentName: string) => {
  switch (componentName) {
    case 'CharacterCreator':
      // Trigger the lazy import
      LazyCharacterCreator;
      break;
    case 'OutlineCreator':
      LazyOutlineCreator;
      break;
    case 'IdeasModule':
      LazyIdeasModule;
      break;
    case 'CharactersTab':
      LazyCharactersTab;
      break;
    case 'LocationsTab':
      LazyLocationsTab;
      break;
    case 'OutlinesTab':
      LazyOutlinesTab;
      break;
    default:
      console.warn(`Unknown component for preloading: ${componentName}`);
  }
};

/**
 * Preload components when user hovers over navigation items
 */
export const preloadOnHover = (targetPath: string) => {
  if (targetPath.includes('/ideas')) {
    preloadComponent('IdeasModule');
  } else if (targetPath.includes('/projects')) {
    preloadComponent('CharactersTab');
    preloadComponent('LocationsTab');
    preloadComponent('OutlinesTab');
  }
};

/**
 * Preload components after initial page load (low priority)
 */
export const preloadAfterInitialLoad = () => {
  // Use requestIdleCallback if available, otherwise setTimeout
  const preload = () => {
    preloadComponent('CharacterCreator');
    preloadComponent('OutlineCreator');
    preloadComponent('IdeasModule');
  };

  if ('requestIdleCallback' in window) {
    requestIdleCallback(preload);
  } else {
    setTimeout(preload, 2000);
  }
};
