/**
 * Validates if a URL points to a valid image
 * @param url The URL to validate
 * @returns A promise that resolves to true if the URL is a valid image, false otherwise
 */
export const validateImageUrl = (url: string): Promise<boolean> => {
  return new Promise((resolve) => {
    const img = new Image();
    img.onload = () => resolve(true);
    img.onerror = () => resolve(false);
    img.src = url;
  });
};
