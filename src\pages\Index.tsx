import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { 
  Lightbulb,
  User,
  BookOpen,
  Layers,
  FileText,
  Clock,
  Sparkles,
  ChevronRight,
  BookText,
  PencilRuler,
  <PERSON><PERSON>,
  <PERSON>,
  FileStack
} from "lucide-react";

const Index = () => {
  const features = [
    {
      icon: <FileStack className="module-icon" />,
      title: "Proyectos",
      description: "Organiza tus historias en proyectos con acceso a todos los módulos creativos.",
      link: "/projects"
    },
    {
      icon: <Lightbulb className="module-icon" />,
      title: "Generador de Ideas",
      description: "Encuentra inspiración con prompts creativos, ejercicios de escritura libre y mapas mentales.",
      link: "/ideas"
    },

    {
      icon: <BookOpen className="module-icon" />,
      title: "Sistema de Documentación",
      description: "Organiza referencias, notas de investigación y etiquetas para tu información.",
      link: "#"
    },
    {
      icon: <Layers className="module-icon" />,
      title: "Constructor de Escaletas",
      description: "Planifica tu historia con escenas, eventos y puntos de giro en una línea temporal.",
      link: "#"
    },
    {
      icon: <FileText className="module-icon" />,
      title: "Organizador de Capítulos",
      description: "Gestiona tus capítulos con un índice dinámico y seguimiento de subtramas.",
      link: "#"
    }
  ];

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section className="relative py-20 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-gray-900 dark:to-gray-800 z-0"></div>
        <div className="container mx-auto px-4 relative z-10">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="w-full lg:w-1/2 mb-10 lg:mb-0">
              <h1 className="text-4xl md:text-6xl font-bold font-heading mb-6">
                Potencia tu proceso de <span className="gradient-text">escritura creativa</span>
              </h1>
              <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-lg">
                Una plataforma integral para escritores que te ayuda desde la concepción 
                de ideas hasta la organización y perfeccionamiento de tu obra.
              </p>
              <div className="flex flex-wrap gap-4">
                <Button size="lg" asChild>
                  <Link to="/projects">
                    Mis Proyectos
                    <ChevronRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button variant="outline" size="lg">
                  Explorar módulos
                </Button>
              </div>
            </div>
            <div className="w-full lg:w-1/2 flex justify-center">
              <div className="relative">
                <div className="animate-pulse-light">
                  <div className="relative w-80 h-80 bg-gradient-to-r from-purple-300 to-indigo-300 dark:from-purple-900 dark:to-indigo-900 rounded-full blur-3xl opacity-20 absolute -top-10 -left-10"></div>
                  <div className="relative w-80 h-80 bg-gradient-to-r from-purple-300 to-indigo-300 dark:from-purple-900 dark:to-indigo-900 rounded-full blur-3xl opacity-20 absolute -bottom-10 -right-10"></div>
                </div>
                <div className="relative w-full max-w-md">
                  <div className="bg-white dark:bg-gray-800 rounded-xl shadow-xl p-8 border border-border">
                    <div className="space-y-4">
                      <div className="h-6 bg-gray-100 dark:bg-gray-700 rounded w-3/4"></div>
                      <div className="h-4 bg-gray-100 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-4 bg-gray-100 dark:bg-gray-700 rounded w-5/6"></div>
                      <div className="h-4 bg-gray-100 dark:bg-gray-700 rounded w-4/6"></div>
                      <div className="h-10 bg-purple-100 dark:bg-purple-900/30 rounded w-full"></div>
                      <div className="flex gap-2 mt-6">
                        <div className="h-8 bg-primary rounded w-28"></div>
                        <div className="h-8 bg-gray-100 dark:bg-gray-700 rounded w-28"></div>
                      </div>
                    </div>
                    <div className="absolute -right-4 -top-4 w-12 h-12 bg-primary rounded-lg flex items-center justify-center text-white">
                      <PencilRuler className="h-6 w-6" />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white dark:bg-gray-900">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl font-bold font-heading mb-4">Módulos Creativos</h2>
            <p className="text-lg text-muted-foreground max-w-xl mx-auto">
              Herramientas especializadas para cada etapa de tu proceso creativo
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <Link 
                to={feature.link} 
                key={index}
                className="feature-card flex flex-col items-center text-center"
              >
                {feature.icon}
                <h3 className="text-xl font-bold mb-2">{feature.title}</h3>
                <p className="text-muted-foreground mb-4">{feature.description}</p>
                {feature.link === "#" ? (
                  <span className="text-sm text-muted-foreground">Próximamente</span>
                ) : (
                  <Button variant="link" className="mt-auto">
                    Acceder
                    <ChevronRight className="ml-1 h-4 w-4" />
                  </Button>
                )}
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50 dark:bg-gray-800">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row items-center">
            <div className="w-full lg:w-1/2 mb-10 lg:mb-0">
              <h2 className="text-3xl font-bold font-heading mb-6">
                Diseñado para potenciar tu creatividad
              </h2>
              <p className="text-lg text-muted-foreground mb-8">
                ScribeHub no es solo una herramienta, es un asistente creativo que te acompaña en cada paso del proceso.
              </p>
              
              <div className="space-y-4">
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-primary/10 p-2 rounded-md">
                    <BookText className="h-5 w-5 text-primary" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium">Guardado automático</h3>
                    <p className="text-muted-foreground">
                      Nunca pierdas tu trabajo. Todo se guarda automáticamente mientras escribes.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-primary/10 p-2 rounded-md">
                    <Palette className="h-5 w-5 text-primary" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium">Interfaz personalizable</h3>
                    <p className="text-muted-foreground">
                      Adapta la interfaz a tu estilo con temas y opciones de personalización.
                    </p>
                  </div>
                </div>
                
                <div className="flex items-start">
                  <div className="flex-shrink-0 bg-primary/10 p-2 rounded-md">
                    <Brain className="h-5 w-5 text-primary" />
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium">Enfoque centrado</h3>
                    <p className="text-muted-foreground">
                      Diseñado para minimizar distracciones y maximizar tu productividad creativa.
                    </p>
                  </div>
                </div>
              </div>
            </div>
            
            <div className="w-full lg:w-1/2 flex justify-center">
              <div className="relative">
                <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-purple-600 rounded-lg blur opacity-30 animate-pulse-light"></div>
                <div className="relative bg-white dark:bg-gray-900 rounded-lg overflow-hidden border border-border shadow-xl">
                  <div className="p-1 bg-secondary">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                      <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                    </div>
                  </div>
                  <div className="p-6">
                    <div className="flex items-center mb-4">
                      <Clock className="h-5 w-5 text-primary mr-2" />
                      <div className="text-sm font-medium">Escritura libre: 5:00</div>
                    </div>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-100 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-3 bg-gray-100 dark:bg-gray-700 rounded w-5/6"></div>
                      <div className="h-3 bg-gray-100 dark:bg-gray-700 rounded w-full"></div>
                      <div className="h-3 bg-gray-100 dark:bg-gray-700 rounded w-4/5"></div>
                      <div className="h-3 bg-gray-100 dark:bg-gray-700 rounded w-full"></div>
                    </div>
                    <div className="mt-4 w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-full overflow-hidden">
                      <div className="h-full bg-primary rounded-full" style={{ width: "60%" }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-20 bg-gradient-to-r from-purple-600 to-indigo-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold font-heading mb-6">
            Comienza tu viaje creativo hoy
          </h2>
          <p className="text-xl text-white/80 mb-8 max-w-2xl mx-auto">
            Transforma tus ideas en historias cautivadoras con nuestras herramientas especializadas
          </p>
          <Button size="lg" variant="secondary" asChild>
            <Link to="/ideas">
              Explorar ScribeHub
            </Link>
          </Button>
        </div>
      </section>
    </div>
  );
};

export default Index;
