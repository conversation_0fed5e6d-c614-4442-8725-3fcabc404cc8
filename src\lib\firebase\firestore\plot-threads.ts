import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  getDoc,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { PlotThread, PlotThreadWithoutId } from "@/types/firestore-models";

/**
 * Get the plot_threads subcollection for a project
 */
export const getPlotThreadsCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "plot_threads");
};

/**
 * Create a new plot thread in a project
 */
export const createPlotThread = async (
  projectId: string, 
  plotThreadData: PlotThreadWithoutId
): Promise<PlotThread> => {
  try {
    const plotThreadsCollection = getPlotThreadsCollection(projectId);
    
    const docRef = await addDoc(plotThreadsCollection, {
      ...plotThreadData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return { 
      id: docRef.id, 
      ...plotThreadData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating plot thread:", error);
    throw error;
  }
};

/**
 * Get all plot threads for a project
 */
export const getProjectPlotThreads = async (projectId: string): Promise<PlotThread[]> => {
  try {
    const plotThreadsCollection = getPlotThreadsCollection(projectId);
    const querySnapshot = await getDocs(plotThreadsCollection);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as PlotThread[];
  } catch (error) {
    console.error("Error fetching project plot threads:", error);
    throw error;
  }
};

/**
 * Get a plot thread by ID
 */
export const getPlotThreadById = async (projectId: string, plotThreadId: string): Promise<PlotThread> => {
  try {
    const plotThreadRef = doc(db, "projects", projectId, "plot_threads", plotThreadId);
    const plotThreadSnap = await getDoc(plotThreadRef);
    
    if (plotThreadSnap.exists()) {
      return { 
        id: plotThreadSnap.id, 
        ...plotThreadSnap.data() 
      } as PlotThread;
    } else {
      throw new Error("Plot thread not found");
    }
  } catch (error) {
    console.error("Error fetching plot thread:", error);
    throw error;
  }
};

/**
 * Update a plot thread
 */
export const updatePlotThread = async (
  projectId: string,
  plotThreadId: string, 
  plotThreadData: Partial<PlotThread>
): Promise<Partial<PlotThread>> => {
  try {
    const plotThreadRef = doc(db, "projects", projectId, "plot_threads", plotThreadId);
    
    const updateData = {
      ...plotThreadData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(plotThreadRef, updateData);
    
    return { 
      id: plotThreadId, 
      ...plotThreadData 
    };
  } catch (error) {
    console.error("Error updating plot thread:", error);
    throw error;
  }
};

/**
 * Delete a plot thread
 */
export const deletePlotThread = async (projectId: string, plotThreadId: string): Promise<{ success: boolean }> => {
  try {
    const plotThreadRef = doc(db, "projects", projectId, "plot_threads", plotThreadId);
    await deleteDoc(plotThreadRef);
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting plot thread:", error);
    throw error;
  }
};

/**
 * Delete all plot threads for a project (used when deleting a project)
 */
export const deleteAllProjectPlotThreads = async (projectId: string): Promise<void> => {
  try {
    const plotThreadsCollection = getPlotThreadsCollection(projectId);
    const querySnapshot = await getDocs(plotThreadsCollection);
    
    const batch = writeBatch(db);
    
    querySnapshot.docs.forEach(plotThreadDoc => {
      const plotThreadRef = doc(db, "projects", projectId, "plot_threads", plotThreadDoc.id);
      batch.delete(plotThreadRef);
    });
    
    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project plot threads:", error);
    throw error;
  }
};

/**
 * Get a reference to a plot thread document
 */
export const getPlotThreadRef = (projectId: string, plotThreadId: string): DocumentReference => {
  return doc(db, "projects", projectId, "plot_threads", plotThreadId);
};
