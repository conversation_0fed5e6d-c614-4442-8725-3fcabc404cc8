import { ReactNode, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { useFirebase } from "@/contexts/FirebaseContext";
import { toast } from "@/components/ui/sonner";

interface ProtectedRouteProps {
  children: ReactNode;
}

const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const { currentUser, isLoading } = useFirebase();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && !currentUser) {
      toast.error("Necesitas iniciar sesión para acceder a esta sección");
    }
  }, [currentUser, isLoading]);

  if (isLoading) {
    // Podríamos mostrar un spinner de carga aquí si quisiéramos
    return null;
  }

  if (!currentUser) {
    // Redirigir a la página de autenticación, guardando la ubicación actual
    // para poder redirigir de vuelta después del inicio de sesión
    return <Navigate to="/auth" state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
