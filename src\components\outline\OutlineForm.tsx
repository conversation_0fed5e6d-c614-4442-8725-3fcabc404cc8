
import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

interface OutlineFormProps {
  title: string;
  description: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const OutlineForm: React.FC<OutlineFormProps> = ({ 
  title, 
  description, 
  onInputChange 
}) => {
  return (
    <div className="space-y-4">
      <div>
        <Label htmlFor="title">Título de la escaleta</Label>
        <Input
          id="title"
          name="title"
          value={title}
          onChange={onInputChange}
          placeholder="Título de la escaleta"
        />
      </div>
      
      <div>
        <Label htmlFor="description">Descripción</Label>
        <Textarea
          id="description"
          name="description"
          value={description}
          onChange={onInputChange}
          placeholder="Descripción de la escaleta"
          rows={3}
        />
      </div>
    </div>
  );
};

export default OutlineForm;
