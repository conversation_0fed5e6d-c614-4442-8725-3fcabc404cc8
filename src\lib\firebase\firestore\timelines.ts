import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  getDoc,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { Timeline, TimelineWithoutId } from "@/types/firestore-models";

/**
 * Get the timelines subcollection for a project
 */
export const getTimelinesCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "timelines");
};

/**
 * Create a new timeline in a project
 */
export const createTimeline = async (
  projectId: string, 
  timelineData: TimelineWithoutId
): Promise<Timeline> => {
  try {
    const timelinesCollection = getTimelinesCollection(projectId);
    
    const docRef = await addDoc(timelinesCollection, {
      ...timelineData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return { 
      id: docRef.id, 
      ...timelineData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating timeline:", error);
    throw error;
  }
};

/**
 * Get all timelines for a project
 */
export const getProjectTimelines = async (projectId: string): Promise<Timeline[]> => {
  try {
    const timelinesCollection = getTimelinesCollection(projectId);
    const querySnapshot = await getDocs(timelinesCollection);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Timeline[];
  } catch (error) {
    console.error("Error fetching project timelines:", error);
    throw error;
  }
};

/**
 * Get a timeline by ID
 */
export const getTimelineById = async (
  projectId: string, 
  timelineId: string
): Promise<Timeline> => {
  try {
    const timelineRef = doc(db, "projects", projectId, "timelines", timelineId);
    const timelineSnap = await getDoc(timelineRef);
    
    if (timelineSnap.exists()) {
      return { 
        id: timelineSnap.id, 
        ...timelineSnap.data() 
      } as Timeline;
    } else {
      throw new Error("Timeline not found");
    }
  } catch (error) {
    console.error("Error fetching timeline:", error);
    throw error;
  }
};

/**
 * Update a timeline
 */
export const updateTimeline = async (
  projectId: string,
  timelineId: string, 
  timelineData: Partial<Timeline>
): Promise<Partial<Timeline>> => {
  try {
    const timelineRef = doc(db, "projects", projectId, "timelines", timelineId);
    
    const updateData = {
      ...timelineData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(timelineRef, updateData);
    
    return { 
      id: timelineId, 
      ...timelineData 
    };
  } catch (error) {
    console.error("Error updating timeline:", error);
    throw error;
  }
};

/**
 * Delete a timeline
 */
export const deleteTimeline = async (
  projectId: string, 
  timelineId: string
): Promise<{ success: boolean }> => {
  try {
    const timelineRef = doc(db, "projects", projectId, "timelines", timelineId);
    await deleteDoc(timelineRef);
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting timeline:", error);
    throw error;
  }
};

/**
 * Delete all timelines for a project (used when deleting a project)
 */
export const deleteAllProjectTimelines = async (projectId: string): Promise<void> => {
  try {
    const timelinesCollection = getTimelinesCollection(projectId);
    const querySnapshot = await getDocs(timelinesCollection);
    
    const batch = writeBatch(db);
    
    querySnapshot.docs.forEach(timelineDoc => {
      const timelineRef = doc(db, "projects", projectId, "timelines", timelineDoc.id);
      batch.delete(timelineRef);
    });
    
    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project timelines:", error);
    throw error;
  }
};

/**
 * Get a reference to a timeline document
 */
export const getTimelineRef = (projectId: string, timelineId: string): DocumentReference => {
  return doc(db, "projects", projectId, "timelines", timelineId);
};
