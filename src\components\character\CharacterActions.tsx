
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Save, RefreshCw, Loader2 } from 'lucide-react';

interface CharacterActionsProps {
  onSave: () => void;
  onReset: () => void;
  saving: boolean;
}

const CharacterActions = ({ onSave, onReset, saving }: CharacterActionsProps) => {
  return (
    <>
      <Button variant="outline" onClick={onReset} disabled={saving}>
        <RefreshCw className="mr-2 h-4 w-4" />
        Limpiar
      </Button>
      <Button onClick={onSave} disabled={saving}>
        {saving ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Guardando...
          </>
        ) : (
          <>
            <Save className="mr-2 h-4 w-4" />
            Guardar Personaje
          </>
        )}
      </Button>
    </>
  );
};

export default CharacterActions;
