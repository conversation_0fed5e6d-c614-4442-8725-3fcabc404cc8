import React from 'react';
import { format } from 'date-fns';
import { CalendarIcon, Shuffle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { cn } from '@/lib/utils';

interface TextFieldProps {
  id: string;
  name: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  placeholder?: string;
  className?: string;
}

export const TextField = ({ 
  id, 
  name, 
  label, 
  value, 
  onChange, 
  placeholder, 
  className 
}: TextFieldProps) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id}>{label}</Label>
      <Input
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
      />
    </div>
  );
};

interface TextAreaFieldProps {
  id: string;
  name: string;
  label: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
}

export const TextAreaField = ({
  id,
  name,
  label,
  value,
  onChange,
  placeholder,
  className,
  minHeight = '80px'
}: TextAreaFieldProps) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id}>{label}</Label>
      <Textarea
        id={id}
        name={name}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        className={`min-h-[${minHeight}]`}
      />
    </div>
  );
};

interface GeneratableTextFieldProps extends TextFieldProps {
  onGenerate: () => void;
  generateTooltip?: string;
}

export const GeneratableTextField = ({
  id,
  name,
  label,
  value,
  onChange,
  placeholder,
  className,
  onGenerate,
  generateTooltip = "Generar aleatoriamente"
}: GeneratableTextFieldProps) => {
  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id}>{label}</Label>
      <div className="flex items-center gap-2">
        <Input
          id={id}
          name={name}
          value={value}
          onChange={onChange}
          placeholder={placeholder}
          className="flex-1"
        />
        <Button 
          variant="outline" 
          size="icon" 
          onClick={onGenerate}
          title={generateTooltip}
        >
          <Shuffle className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
};

interface DatePickerFieldProps {
  id: string;
  label: string;
  value?: Date;
  onChange: (date: Date | undefined) => void;
  placeholder?: string;
  className?: string;
}

export const DatePickerField = ({
  id,
  label,
  value,
  onChange,
  placeholder = "Seleccionar fecha",
  className
}: DatePickerFieldProps) => {
  const isValidDate = React.useMemo(() => {
    if (!value) return false;
    
    if (!(value instanceof Date)) return false;
    
    return !isNaN(value.getTime());
  }, [value]);
  
  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id}>{label}</Label>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            id={id}
            variant="outline"
            className={cn(
              "w-full justify-start text-left font-normal",
              !isValidDate && "text-muted-foreground"
            )}
          >
            <CalendarIcon className="mr-2 h-4 w-4" />
            {isValidDate ? format(value as Date, "dd/MM/yyyy") : placeholder}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-auto p-0" align="start">
          <Calendar
            mode="single"
            selected={isValidDate ? value : undefined}
            onSelect={onChange}
            initialFocus
            className={cn("p-3 pointer-events-auto")}
          />
        </PopoverContent>
      </Popover>
    </div>
  );
};
