import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { X, Plus } from 'lucide-react';
import { Badge } from '@/components/ui/badge';

interface ArrayFieldProps {
  id: string;
  name: string;
  label: string;
  values: string[];
  onAddItem: (name: string, value: string) => void;
  onRemoveItem: (name: string, value: string) => void;
  placeholder?: string;
  className?: string;
  badgeColor?: string;
}

export const ArrayField = ({
  id,
  name,
  label,
  values = [],
  onAddItem,
  onRemoveItem,
  placeholder = 'Añadir nuevo elemento...',
  className,
  badgeColor
}: ArrayFieldProps) => {
  const [newValue, setNewValue] = useState('');

  const handleAddItem = () => {
    if (newValue.trim() !== '') {
      onAddItem(name, newValue.trim());
      setNewValue('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddItem();
    }
  };

  return (
    <div className={`space-y-2 ${className}`}>
      <Label htmlFor={id}>{label}</Label>
      <div className="flex items-center gap-2">
        <Input
          id={id}
          value={newValue}
          onChange={(e) => setNewValue(e.target.value)}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className="flex-1"
        />
        <Button 
          type="button"
          variant="outline" 
          size="icon" 
          onClick={handleAddItem}
          title="Añadir"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>
      
      {values.length > 0 && (
        <div className="flex flex-wrap gap-2 mt-2">
          {values.map((value, index) => (
            <Badge 
              key={`${value}-${index}`} 
              variant="secondary"
              style={badgeColor ? { backgroundColor: badgeColor, color: '#fff' } : undefined}
              className="flex items-center gap-1 px-2 py-1"
            >
              {value}
              <Button
                type="button"
                variant="ghost"
                size="icon"
                className="h-4 w-4 p-0 ml-1 hover:bg-transparent"
                onClick={() => onRemoveItem(name, value)}
              >
                <X className="h-3 w-3" />
              </Button>
            </Badge>
          ))}
        </div>
      )}
    </div>
  );
};

export default ArrayField;
