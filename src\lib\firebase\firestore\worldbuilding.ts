import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  getDoc,
  query,
  where,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { WorldbuildingElement, WorldbuildingElementWithoutId } from "@/types/firestore-models";

/**
 * Get the worldbuilding_elements subcollection for a project
 */
export const getWorldbuildingElementsCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "worldbuilding_elements");
};

/**
 * Create a new worldbuilding element in a project
 */
export const createWorldbuildingElement = async (
  projectId: string, 
  elementData: WorldbuildingElementWithoutId
): Promise<WorldbuildingElement> => {
  try {
    const elementsCollection = getWorldbuildingElementsCollection(projectId);
    
    const docRef = await addDoc(elementsCollection, {
      ...elementData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return { 
      id: docRef.id, 
      ...elementData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating worldbuilding element:", error);
    throw error;
  }
};

/**
 * Get all worldbuilding elements for a project
 */
export const getProjectWorldbuildingElements = async (projectId: string): Promise<WorldbuildingElement[]> => {
  try {
    const elementsCollection = getWorldbuildingElementsCollection(projectId);
    const querySnapshot = await getDocs(elementsCollection);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as WorldbuildingElement[];
  } catch (error) {
    console.error("Error fetching project worldbuilding elements:", error);
    throw error;
  }
};

/**
 * Get worldbuilding elements by type
 */
export const getWorldbuildingElementsByType = async (
  projectId: string, 
  type: string
): Promise<WorldbuildingElement[]> => {
  try {
    const elementsCollection = getWorldbuildingElementsCollection(projectId);
    const q = query(elementsCollection, where("type", "==", type));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as WorldbuildingElement[];
  } catch (error) {
    console.error("Error fetching worldbuilding elements by type:", error);
    throw error;
  }
};

/**
 * Get a worldbuilding element by ID
 */
export const getWorldbuildingElementById = async (
  projectId: string, 
  elementId: string
): Promise<WorldbuildingElement> => {
  try {
    const elementRef = doc(db, "projects", projectId, "worldbuilding_elements", elementId);
    const elementSnap = await getDoc(elementRef);
    
    if (elementSnap.exists()) {
      return { 
        id: elementSnap.id, 
        ...elementSnap.data() 
      } as WorldbuildingElement;
    } else {
      throw new Error("Worldbuilding element not found");
    }
  } catch (error) {
    console.error("Error fetching worldbuilding element:", error);
    throw error;
  }
};

/**
 * Update a worldbuilding element
 */
export const updateWorldbuildingElement = async (
  projectId: string,
  elementId: string, 
  elementData: Partial<WorldbuildingElement>
): Promise<Partial<WorldbuildingElement>> => {
  try {
    const elementRef = doc(db, "projects", projectId, "worldbuilding_elements", elementId);
    
    const updateData = {
      ...elementData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(elementRef, updateData);
    
    return { 
      id: elementId, 
      ...elementData 
    };
  } catch (error) {
    console.error("Error updating worldbuilding element:", error);
    throw error;
  }
};

/**
 * Delete a worldbuilding element
 */
export const deleteWorldbuildingElement = async (
  projectId: string, 
  elementId: string
): Promise<{ success: boolean }> => {
  try {
    const elementRef = doc(db, "projects", projectId, "worldbuilding_elements", elementId);
    await deleteDoc(elementRef);
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting worldbuilding element:", error);
    throw error;
  }
};

/**
 * Delete all worldbuilding elements for a project (used when deleting a project)
 */
export const deleteAllProjectWorldbuildingElements = async (projectId: string): Promise<void> => {
  try {
    const elementsCollection = getWorldbuildingElementsCollection(projectId);
    const querySnapshot = await getDocs(elementsCollection);
    
    const batch = writeBatch(db);
    
    querySnapshot.docs.forEach(elementDoc => {
      const elementRef = doc(db, "projects", projectId, "worldbuilding_elements", elementDoc.id);
      batch.delete(elementRef);
    });
    
    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project worldbuilding elements:", error);
    throw error;
  }
};

/**
 * Get a reference to a worldbuilding element document
 */
export const getWorldbuildingElementRef = (projectId: string, elementId: string): DocumentReference => {
  return doc(db, "projects", projectId, "worldbuilding_elements", elementId);
};
