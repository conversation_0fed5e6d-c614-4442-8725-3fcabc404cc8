import { useParams, useNavigate } from 'react-router-dom';
import { useState } from 'react';
import { Separator } from '@/components/ui/separator';
import { Button } from '@/components/ui/button';
import { ArrowLeft, Edit, MapPin } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import LocationCreator from '@/components/location/LocationCreator';
import LoadingState from '@/components/project/LoadingState';
import ProjectNotFound from '@/components/project/ProjectNotFound';
import { useLocationDetail } from '@/hooks/useLocationDetail';
import { Location } from '@/types/firestore-models';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageModal } from '@/components/ui/image-modal';

const LocationDetailPage = () => {
  const { projectId, locationId } = useParams<{ projectId: string, locationId: string }>();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [editMode, setEditMode] = useState(false);
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  const { location, project, isLoading, setLocation } = useLocationDetail({ projectId, locationId });

  const handleImageClick = () => {
    if (location?.image_url) {
      setIsImageModalOpen(true);
    }
  };

  const handleLocationUpdated = (updatedLocation: Location) => {
    setLocation(updatedLocation);
    setEditMode(false);
    toast({
      title: "Localización actualizada",
      description: "Los cambios han sido guardados correctamente",
    });
  };

  const handleBackToProject = () => {
    if (project) {
      navigate(`/projects/${project.id}`);
    } else {
      navigate('/projects');
    }
  };

  if (isLoading) {
    return <LoadingState />;
  }

  if (!location || !project) {
    return <ProjectNotFound />;
  }

  if (editMode) {
    return (
      <LocationCreator
        projectId={project.id}
        existingLocation={location}
        onLocationCreated={() => {}}
        onLocationUpdated={handleLocationUpdated}
        onCancel={() => setEditMode(false)}
      />
    );
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="flex items-center mb-6">
        <Button variant="ghost" onClick={handleBackToProject} className="mr-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          Volver al proyecto
        </Button>
        <h1 className="text-3xl font-bold flex items-center">
          <MapPin className="h-6 w-6 mr-2 text-primary" />
          {location.name}
        </h1>
        <Button
          variant="outline"
          className="ml-auto"
          onClick={() => setEditMode(true)}
        >
          <Edit className="mr-2 h-4 w-4" />
          Editar localización
        </Button>
      </div>

      <Separator className="mb-6" />

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {/* Columna izquierda - Imagen y detalles básicos */}
        <div className="space-y-6">
          {/* Imagen */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Imagen</CardTitle>
            </CardHeader>
            <CardContent>
              {location.image_url ? (
                <div className="relative rounded-md overflow-hidden group">
                  <img
                    src={location.image_url}
                    alt={location.name}
                    className="w-full h-auto object-cover cursor-pointer transition-all duration-200 group-hover:brightness-90"
                    onClick={handleImageClick}
                    title="Haz clic para ampliar"
                  />
                  <div
                    className="absolute inset-0 flex items-center justify-center bg-black/0 opacity-0 group-hover:opacity-100 group-hover:bg-black/20 transition-all duration-200 cursor-pointer"
                    onClick={handleImageClick}
                  >
                    <span className="bg-black/60 text-white px-3 py-1 rounded-full text-sm font-medium">
                      Ver imagen
                    </span>
                  </div>
                </div>
              ) : (
                <div className="w-full h-48 bg-muted rounded-md flex items-center justify-center">
                  <span className="text-muted-foreground">Sin imagen</span>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Información básica */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Información básica</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {location.type && (
                <div>
                  <h3 className="text-sm font-medium">Tipo</h3>
                  <p>{location.type}</p>
                </div>
              )}
              {location.ubicacion_general && (
                <div>
                  <h3 className="text-sm font-medium">Ubicación general</h3>
                  <p>{location.ubicacion_general}</p>
                </div>
              )}
              {location.latitud_longitud && (
                <div>
                  <h3 className="text-sm font-medium">Coordenadas</h3>
                  <p>{location.latitud_longitud}</p>
                </div>
              )}
              {location.alias && location.alias.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium">Nombres alternativos</h3>
                  <div className="flex flex-wrap gap-2 mt-1">
                    {location.alias.map((alias, index) => (
                      <span
                        key={index}
                        className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md text-sm"
                      >
                        {alias}
                      </span>
                    ))}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Columna derecha - Descripción y detalles sensoriales */}
        <div className="md:col-span-2 space-y-6">
          {/* Descripción */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Descripción</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-line">
                {location.description || "Sin descripción"}
              </p>
            </CardContent>
          </Card>

          {/* Importancia para la historia */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Importancia para la historia</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="whitespace-pre-line">
                {location.importance_to_story || "No especificada"}
              </p>
            </CardContent>
          </Card>

          {/* Detalles sensoriales */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Detalles sensoriales</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {location.sensory_details?.sight && (
                <div>
                  <h3 className="text-sm font-medium">Vista</h3>
                  <p className="whitespace-pre-line">{location.sensory_details.sight}</p>
                </div>
              )}
              {location.sensory_details?.sound && (
                <div>
                  <h3 className="text-sm font-medium">Sonido</h3>
                  <p className="whitespace-pre-line">{location.sensory_details.sound}</p>
                </div>
              )}
              {location.sensory_details?.smell && (
                <div>
                  <h3 className="text-sm font-medium">Olor</h3>
                  <p className="whitespace-pre-line">{location.sensory_details.smell}</p>
                </div>
              )}
              {location.sensory_details?.taste && (
                <div>
                  <h3 className="text-sm font-medium">Sabor</h3>
                  <p className="whitespace-pre-line">{location.sensory_details.taste}</p>
                </div>
              )}
              {location.sensory_details?.touch && (
                <div>
                  <h3 className="text-sm font-medium">Tacto</h3>
                  <p className="whitespace-pre-line">{location.sensory_details.touch}</p>
                </div>
              )}
              {location.sensory_details?.emotional_atmosphere && (
                <div>
                  <h3 className="text-sm font-medium">Ambiente emocional</h3>
                  <p className="whitespace-pre-line">{location.sensory_details.emotional_atmosphere}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Notas adicionales */}
          {location.notas_adicionales && (
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Notas adicionales</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="whitespace-pre-line">{location.notas_adicionales}</p>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Modal para visualización de imagen */}
      {location && location.image_url && (
        <ImageModal
          isOpen={isImageModalOpen}
          onClose={() => setIsImageModalOpen(false)}
          imageUrl={location.image_url}
          altText={`Imagen de ${location.name}`}
        />
      )}
    </div>
  );
};

export default LocationDetailPage;
