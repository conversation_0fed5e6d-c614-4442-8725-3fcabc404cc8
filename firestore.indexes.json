{"indexes": [{"collectionGroup": "characters", "queryScope": "COLLECTION", "fields": [{"fieldPath": "projectId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "locations", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "name", "order": "ASCENDING"}]}, {"collectionGroup": "outlines", "queryScope": "COLLECTION", "fields": [{"fieldPath": "projectId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}], "fieldOverrides": []}