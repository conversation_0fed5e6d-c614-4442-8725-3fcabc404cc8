
import { useParams } from 'react-router-dom';
import { Separator } from '@/components/ui/separator';
import { useOutlineDetail } from '@/hooks/useOutlineDetailRefactored';
import OutlineCreator from '@/components/outline/OutlineCreator';
import LoadingState from '@/components/project/LoadingState';
import ProjectNotFound from '@/components/project/ProjectNotFound';
import OutlineHeader from '@/components/outline/OutlineHeader';

const OutlineDetailPage = () => {
  const { projectId, outlineId } = useParams<{ projectId: string, outlineId: string }>();

  const {
    outline,
    project,
    characters,
    isLoading,
    isConverting,
    handleUpdateOutline,
    handleNavigateBack,
    handleConvertToScenes,
  } = useOutlineDetail({ projectId, outlineId });

  if (isLoading || isConverting) {
    return <LoadingState />;
  }

  if (!outline || !project) {
    return <ProjectNotFound />;
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <OutlineHeader
        outline={outline}
        projectTitle={project.title}
        onBackClick={handleNavigateBack}
        onConvertToScenes={handleConvertToScenes}
      />

      <Separator className="mb-6" />

      <OutlineCreator
        projectId={project.id}
        existingOutline={outline}
        characters={characters}
        onOutlineUpdated={handleUpdateOutline}
        onCancel={handleNavigateBack}
      />
    </div>
  );
};

export default OutlineDetailPage;
