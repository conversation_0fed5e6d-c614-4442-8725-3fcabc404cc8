
import { Suspense, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import Index from "./pages/Index";
import NotFound from "./pages/NotFound";
import {
  LazyIdeasPage,
  LazyCharacterDetailPage,
  LazyLocationDetailPage,
  LazyAuthPage,
  LazyProjectsPage,
  LazyProjectDetailPage,
  LazyOutlineDetailPage
} from "./pages/lazy-pages";
import { PageLoadingFallback } from "@/components/ui/loading-fallback";
import { preloadAfterInitialLoad } from "@/utils/preload-components";
import { ThemeProvider } from "@/components/ThemeProvider";
import Navbar from "@/components/Navbar";
import { FirebaseProvider } from "./contexts/FirebaseContext";
import ProtectedRoute from "@/components/ProtectedRoute";

const queryClient = new QueryClient();

const App = () => {
  // Preload components after initial load
  useEffect(() => {
    preloadAfterInitialLoad();
  }, []);

  return (
  <QueryClientProvider client={queryClient}>
    <ThemeProvider defaultTheme="light">
      <FirebaseProvider>
        <TooltipProvider>
          <Toaster />
          <Sonner />
          <BrowserRouter>
            <div className="min-h-screen flex flex-col">
              <Navbar />
              <main className="flex-1">
                <Routes>
                  <Route path="/" element={<Index />} />
                  <Route path="/auth" element={
                    <Suspense fallback={<PageLoadingFallback message="Cargando autenticación..." />}>
                      <LazyAuthPage />
                    </Suspense>
                  } />

                  {/* Rutas protegidas que requieren autenticación */}
                  <Route path="/ideas" element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoadingFallback message="Cargando generador de ideas..." />}>
                        <LazyIdeasPage />
                      </Suspense>
                    </ProtectedRoute>
                  } />

                  <Route path="/projects/:projectId/characters/:characterId" element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoadingFallback message="Cargando detalle del personaje..." />}>
                        <LazyCharacterDetailPage />
                      </Suspense>
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId/locations/:locationId" element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoadingFallback message="Cargando detalle de localización..." />}>
                        <LazyLocationDetailPage />
                      </Suspense>
                    </ProtectedRoute>
                  } />
                  <Route path="/projects" element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoadingFallback message="Cargando proyectos..." />}>
                        <LazyProjectsPage />
                      </Suspense>
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId" element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoadingFallback message="Cargando proyecto..." />}>
                        <LazyProjectDetailPage />
                      </Suspense>
                    </ProtectedRoute>
                  } />
                  <Route path="/projects/:projectId/outlines/:outlineId" element={
                    <ProtectedRoute>
                      <Suspense fallback={<PageLoadingFallback message="Cargando escaleta..." />}>
                        <LazyOutlineDetailPage />
                      </Suspense>
                    </ProtectedRoute>
                  } />

                  <Route path="*" element={<NotFound />} />
                </Routes>
              </main>
            </div>
          </BrowserRouter>
        </TooltipProvider>
      </FirebaseProvider>
    </ThemeProvider>
  </QueryClientProvider>
  );
};

export default App;
