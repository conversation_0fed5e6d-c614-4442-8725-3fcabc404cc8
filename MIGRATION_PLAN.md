# Plan de Migración y Eliminación de Código Legacy

## 🎯 Objetivo
Completar la migración a la estructura de subcolecciones y eliminar todo el código legacy para simplificar el mantenimiento y evitar confusiones.

## 📋 Inventario de Código Legacy Identificado

### 1. **Archivos Legacy para Eliminar**
- `src/components/CharacterCreator.tsx` (legacy)
- `src/lib/firebase/characters.ts` (legacy - colección raíz)
- `src/lib/firebase/outlines.ts` (legacy - colección raíz)
- `src/lib/firebase/projects.ts` (legacy - referencias a colecciones raíz)
- `src/lib/firebase/index.ts` (exportaciones legacy)
- `firestore.rules.example` (archivo de ejemplo obsoleto)

### 2. **Funciones Legacy para Eliminar**
- `getProjectByIdLegacy`
- `getCharacterByIdLegacy`
- `getOutlineByIdLegacy`
- `deleteProjectLegacy`
- `deleteCharacterLegacy`
- `getProjectCharactersLegacy`
- `getProjectOutlinesLegacy`

### 3. **Referencias a Colecciones Raíz para Migrar**
- Referencias a `collection(db, "characters")`
- Referencias a `collection(db, "outlines")`
- Queries que buscan en colecciones raíz

### 4. **TODOs Pendientes en Migración**
- `migrate-data.ts`: Completar migración de `chapter_id` en escenas
- Asegurar consistencia en uso de DocumentReference vs IDs

## 🚀 Plan de Ejecución

### Fase 1: Verificar Dependencias
1. Identificar todos los archivos que importan funciones legacy
2. Verificar que existen alternativas en la nueva estructura
3. Documentar cambios necesarios

### Fase 2: Actualizar Referencias
1. Reemplazar importaciones legacy con nuevas funciones
2. Actualizar llamadas a funciones legacy
3. Migrar queries de colecciones raíz a subcolecciones

### Fase 3: Completar Migración de Datos
1. Finalizar migración de `chapter_id` en escenas
2. Asegurar consistencia en DocumentReference
3. Validar integridad de datos

### Fase 4: Eliminar Código Legacy
1. Eliminar archivos legacy
2. Limpiar exportaciones en `index.ts`
3. Actualizar reglas de Firestore

### Fase 5: Verificación y Pruebas
1. Verificar que la aplicación funciona correctamente
2. Probar todas las funcionalidades
3. Validar que no hay referencias rotas

## 📊 Estado Actual

### ✅ Ya Migrado
- Estructura de subcolecciones implementada
- Nuevas funciones de Firestore funcionando
- Tipos de datos actualizados

### ⚠️ Pendiente de Migración
- Eliminar archivos legacy
- Actualizar todas las referencias
- Completar migración de datos
- Limpiar reglas de Firestore

## 🔍 Archivos a Revisar

### Archivos que Pueden Usar Funciones Legacy
- `src/pages/ProjectDetailPage.tsx`
- `src/pages/ProjectsPage.tsx`
- `src/components/project/*.tsx`
- `src/hooks/*.ts`

### Archivos de Migración
- `src/lib/firebase/migration/migrate-data.ts`
- `src/lib/firebase/migration-helpers.ts`

## 🎯 Resultado Esperado

### Después de la Migración
- ✅ Solo estructura de subcolecciones
- ✅ Código limpio sin duplicaciones
- ✅ Funciones consistentes
- ✅ Reglas de Firestore simplificadas
- ✅ Mantenimiento más fácil
- ✅ Menos superficie de ataque de bugs

### Beneficios
- **Simplicidad**: Una sola forma de hacer las cosas
- **Consistencia**: Todas las entidades en subcolecciones
- **Mantenibilidad**: Menos código para mantener
- **Seguridad**: Reglas más simples y claras
- **Performance**: Queries más eficientes
