
import { ReactNode } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';

interface ModuleCardProps {
  id: string;
  name: string;
  icon: ReactNode;
  count: number;
  onClick: (tabId: string) => void;
}

const ModuleCard = ({ id, name, icon, count, onClick }: ModuleCardProps) => {
  return (
    <Card
      className="overflow-hidden border border-border hover:shadow-md transition-shadow cursor-pointer"
      onClick={() => onClick(id)}
    >
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center">
          <div className="bg-primary/10 p-2 rounded mr-2">
            {icon}
          </div>
          {name}
        </CardTitle>
      </CardHeader>
      <CardContent className="pb-2">
        <p className="text-sm text-muted-foreground">
          {count} {id === 'characters' ? 'personajes' :
                  id === 'locations' ? 'localizaciones' :
                  id === 'documentation' ? 'documentos' :
                  id === 'outline' ? 'escaletas' :
                  id === 'chapters' ? 'capítulos' : 'asistentes'}
        </p>
      </CardContent>
      <CardFooter>
        <Button variant="ghost" className="w-full justify-start">
          {id === 'documentation' || id === 'chapters' || id === 'assistant'
            ? 'Próximamente'
            : `Gestionar ${id === 'characters' ? 'personajes' :
                          id === 'locations' ? 'localizaciones' :
                          id === 'outline' ? 'escaletas' : ''}`}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default ModuleCard;
