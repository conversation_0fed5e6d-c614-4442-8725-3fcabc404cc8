
import { useState } from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Upload, Link as LinkIcon, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { validateImageUrl } from './ImageUrlValidator';

interface ProfilePictureUploaderProps {
  name: string;
  profilePicture?: string;
  onFileChange: (url: string) => void;
}

const ProfilePictureUploader = ({ name, profilePicture, onFileChange }: ProfilePictureUploaderProps) => {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [imageUrl, setImageUrl] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState<string>('upload');

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setError(null);

      // Create URL for preview
      const fileUrl = URL.createObjectURL(file);
      onFileChange(fileUrl);
    }
  };

  const handleUrlChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setImageUrl(e.target.value);
    setError(null);
  };

  // Using the imported validateImageUrl function

  const handleLoadFromUrl = async () => {
    if (!imageUrl.trim()) {
      setError('Por favor, introduce una URL de imagen');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const isValid = await validateImageUrl(imageUrl);

      if (isValid) {
        onFileChange(imageUrl);
        setError(null);
      } else {
        setError('La URL no corresponde a una imagen válida');
      }
    } catch (err) {
      setError('Error al cargar la imagen. Verifica la URL e intenta de nuevo.');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="flex flex-col items-center">
      <Label htmlFor="profilePicture" className="mb-2 text-sm font-medium">Imagen del Personaje</Label>

      <div className="relative group mb-4">
        <Avatar className="h-32 w-32 mb-2 border-2 border-primary/20">
          {profilePicture ? (
            <AvatarImage src={profilePicture} alt={name || "Personaje"} className="object-cover" />
          ) : (
            <AvatarFallback className="bg-secondary text-xl">
              {name ? name.substring(0, 2).toUpperCase() : "PC"}
            </AvatarFallback>
          )}
        </Avatar>

        <div className="absolute inset-0 flex items-center justify-center bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-full">
          <Input
            id="profilePicture"
            type="file"
            accept="image/*"
            onChange={handleFileChange}
            className="sr-only"
          />
          <Label
            htmlFor="profilePicture"
            className="cursor-pointer flex items-center justify-center gap-1 text-white"
          >
            <Upload className="h-5 w-5" />
            <span className="text-sm">Cambiar</span>
          </Label>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full max-w-xs">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="upload">
            <Upload className="h-4 w-4 mr-2" />
            Archivo
          </TabsTrigger>
          <TabsTrigger value="url">
            <LinkIcon className="h-4 w-4 mr-2" />
            URL
          </TabsTrigger>
        </TabsList>

        <TabsContent value="upload" className="mt-2">
          <div className="text-center">
            <Button
              variant="outline"
              className="w-full"
              onClick={() => document.getElementById('profilePicture')?.click()}
            >
              <Upload className="h-4 w-4 mr-2" />
              Seleccionar archivo
            </Button>
            {selectedFile && (
              <p className="text-xs mt-2 text-muted-foreground">
                {selectedFile.name}
              </p>
            )}
          </div>
        </TabsContent>

        <TabsContent value="url" className="mt-2 space-y-2">
          <div className="space-y-2">
            <Input
              type="url"
              placeholder="https://ejemplo.com/imagen.jpg"
              value={imageUrl}
              onChange={handleUrlChange}
            />
            <Button
              className="w-full"
              onClick={handleLoadFromUrl}
              disabled={isLoading}
            >
              {isLoading ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Cargando...
                </>
              ) : (
                <>
                  <LinkIcon className="h-4 w-4 mr-2" />
                  Cargar imagen
                </>
              )}
            </Button>
            {error && (
              <p className="text-xs text-destructive mt-1">{error}</p>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default ProfilePictureUploader;
