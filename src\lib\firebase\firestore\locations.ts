import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { Location, LocationWithoutId } from "@/types/firestore-models";

/**
 * Get the locations subcollection for a project
 */
export const getLocationsCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "locations");
};

/**
 * Create a new location in a project
 */
export const createLocation = async (
  projectId: string,
  locationData: LocationWithoutId
): Promise<Location> => {
  try {
    const locationsCollection = getLocationsCollection(projectId);

    const docRef = await addDoc(locationsCollection, {
      ...locationData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      id: docRef.id,
      ...locationData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating location:", error);
    throw error;
  }
};

/**
 * Get all locations for a project
 */
export const getProjectLocations = async (projectId: string): Promise<Location[]> => {
  try {
    // First, verify that the project exists and the user has access to it
    const projectRef = doc(db, "projects", projectId);
    const projectSnap = await getDoc(projectRef);

    if (!projectSnap.exists()) {
      throw new Error("Project not found");
    }

    // Store the project data for permission checks
    const projectData = projectSnap.data();
    console.log("Project data for location permission check:", {
      projectId,
      userId: projectData.userId,
      currentAuth: "Using Firebase Auth"
    });

    try {
      const locationsCollection = getLocationsCollection(projectId);
      const querySnapshot = await getDocs(locationsCollection);

      const locations = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Location[];

      console.log(`Retrieved ${locations.length} locations from project`);
      return locations;
    } catch (error) {
      console.error("Error fetching project locations:", error);
      // Return empty array instead of throwing to prevent app crashes
      return [];
    }
  } catch (error) {
    console.error("Error in location retrieval process:", error);
    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
};

/**
 * Get a location by ID
 */
export const getLocationById = async (projectId: string, locationId: string): Promise<Location> => {
  try {
    const locationRef = doc(db, "projects", projectId, "locations", locationId);
    const locationSnap = await getDoc(locationRef);

    if (locationSnap.exists()) {
      return {
        id: locationSnap.id,
        ...locationSnap.data()
      } as Location;
    } else {
      throw new Error("Location not found");
    }
  } catch (error) {
    console.error("Error fetching location:", error);
    throw error;
  }
};

/**
 * Update a location
 */
export const updateLocation = async (
  projectId: string,
  locationId: string,
  locationData: Partial<Location>
): Promise<Partial<Location>> => {
  try {
    const locationRef = doc(db, "projects", projectId, "locations", locationId);

    const updateData = {
      ...locationData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(locationRef, updateData);

    return {
      id: locationId,
      ...locationData
    };
  } catch (error) {
    console.error("Error updating location:", error);
    throw error;
  }
};

/**
 * Delete a location
 */
export const deleteLocation = async (projectId: string, locationId: string): Promise<{ success: boolean }> => {
  try {
    const locationRef = doc(db, "projects", projectId, "locations", locationId);
    await deleteDoc(locationRef);

    return { success: true };
  } catch (error) {
    console.error("Error deleting location:", error);
    throw error;
  }
};

/**
 * Delete all locations for a project (used when deleting a project)
 */
export const deleteAllProjectLocations = async (projectId: string): Promise<void> => {
  try {
    const locationsCollection = getLocationsCollection(projectId);
    const querySnapshot = await getDocs(locationsCollection);

    const batch = writeBatch(db);

    querySnapshot.docs.forEach(locationDoc => {
      const locationRef = doc(db, "projects", projectId, "locations", locationDoc.id);
      batch.delete(locationRef);
    });

    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project locations:", error);
    throw error;
  }
};

/**
 * Get a reference to a location document
 */
export const getLocationRef = (projectId: string, locationId: string): DocumentReference => {
  return doc(db, "projects", projectId, "locations", locationId);
};
