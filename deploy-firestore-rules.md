# Guía de Despliegue de Reglas de Firestore

## Pasos para Desplegar las Nuevas Reglas

### 1. Verificar las Reglas Localmente

Antes de desplegar, es recomendable probar las reglas usando el emulador de Firestore:

```bash
# Iniciar el emulador de Firestore
firebase emulators:start --only firestore

# En otra terminal, ejecutar las pruebas
node test-firestore-rules.js
```

### 2. Validar las Reglas

```bash
# Validar la sintaxis de las reglas
firebase firestore:rules:validate
```

### 3. <PERSON><PERSON><PERSON> las Reglas

```bash
# Desplegar solo las reglas de Firestore
firebase deploy --only firestore:rules
```

### 4. Verificar el Despliegue

```bash
# Verificar que las reglas se desplegaron correctamente
firebase firestore:rules:get
```

## Comandos Útiles

### Ver las Reglas Actuales en Producción
```bash
firebase firestore:rules:get
```

### Desplegar Reglas y Índices
```bash
firebase deploy --only firestore
```

### Rollback (si es necesario)
Si necesitas revertir las reglas, puedes:

1. Restaurar el archivo `firestore.rules` desde el control de versiones
2. Ejecutar `firebase deploy --only firestore:rules`

## Monitoreo Post-Despliegue

### 1. Verificar Logs de Firestore
- Ve a la consola de Firebase
- Navega a Firestore > Uso
- Revisa los logs para errores de permisos

### 2. Probar Funcionalidad
- Crear un nuevo proyecto
- Crear personajes, localizaciones, escaletas
- Verificar que los usuarios existentes pueden acceder a sus datos

### 3. Monitorear Errores
```bash
# Ver logs en tiempo real
firebase functions:log --only firestore
```

## Checklist de Verificación

- [ ] ✅ Reglas validadas localmente
- [ ] ✅ Pruebas ejecutadas exitosamente
- [ ] ✅ Sintaxis de reglas validada
- [ ] ✅ Reglas desplegadas
- [ ] ✅ Funcionalidad verificada en producción
- [ ] ✅ No hay errores en los logs
- [ ] ✅ Usuarios existentes pueden acceder a sus datos

## Notas Importantes

1. **Compatibilidad**: Las nuevas reglas son compatibles con el código existente
2. **Sin Downtime**: El despliegue de reglas no causa downtime
3. **Reversible**: Las reglas se pueden revertir fácilmente si es necesario
4. **Seguridad**: Las nuevas reglas mejoran la seguridad sin afectar la funcionalidad

## Contacto de Emergencia

Si hay problemas después del despliegue:
1. Revertir las reglas inmediatamente
2. Revisar los logs de errores
3. Contactar al equipo de desarrollo
