import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Placeholder from '@tiptap/extension-placeholder';
import CharacterCount from '@tiptap/extension-character-count';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import Highlight from '@tiptap/extension-highlight';
import TextAlign from '@tiptap/extension-text-align';
import Strike from '@tiptap/extension-strike';
import Subscript from '@tiptap/extension-subscript';
import Superscript from '@tiptap/extension-superscript';
import Underline from '@tiptap/extension-underline';
import Link from '@tiptap/extension-link';
import HorizontalRule from '@tiptap/extension-horizontal-rule';
import Mention from '@tiptap/extension-mention';
import Table from '@tiptap/extension-table';
import TableRow from '@tiptap/extension-table-row';
import TableHeader from '@tiptap/extension-table-header';
import TableCell from '@tiptap/extension-table-cell';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import {
  Bold,
  Italic,
  Underline as UnderlineIcon,
  Strikethrough,
  Subscript as SubscriptIcon,
  Superscript as SuperscriptIcon,
  List,
  ListOrdered,
  Quote,
  Undo,
  Redo,
  Type,
  AlignLeft,
  AlignCenter,
  AlignRight,
  AlignJustify,
  FileText,
  Palette,
  Highlighter,
  Link as LinkIcon,
  Minus,
  Eraser,
  Maximize,
  Minimize,
  Search,
  Table as TableIcon,
  Save,
  AtSign,
  Plus,
  Trash2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useState, useCallback, useEffect, useRef } from 'react';
import tippy from 'tippy.js';

interface RichTextEditorProps {
  value: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
  minHeight?: string;
  showWordCount?: boolean;
  // Nuevas props para Fase 2
  autoSave?: boolean;
  autoSaveDelay?: number;
  onAutoSave?: (content: string) => void;
  characters?: Array<{ id: string; name: string }>;
  locations?: Array<{ id: string; name: string }>;
  projectId?: string;
}

const RichTextEditor = ({
  value,
  onChange,
  placeholder = "Comienza a escribir tu escena...",
  className = "",
  minHeight = "400px",
  showWordCount = true,
  autoSave = false,
  autoSaveDelay = 2000,
  onAutoSave,
  characters = [],
  locations = [],
  projectId
}: RichTextEditorProps) => {
  // Estados existentes
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [showHighlightPicker, setShowHighlightPicker] = useState(false);
  const [linkUrl, setLinkUrl] = useState('');
  const [showLinkDialog, setShowLinkDialog] = useState(false);

  // Nuevos estados para Fase 2
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showSearchDialog, setShowSearchDialog] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [replaceTerm, setReplaceTerm] = useState('');
  const [autoSaveStatus, setAutoSaveStatus] = useState<'idle' | 'saving' | 'saved' | 'error'>('idle');
  const [lastSaved, setLastSaved] = useState<Date | null>(null);

  // Configuración de menciones
  const mentionSuggestion = {
    items: ({ query }: { query: string }) => {
      const allEntities = [
        ...characters.map(char => ({ ...char, type: 'character' })),
        ...locations.map(loc => ({ ...loc, type: 'location' }))
      ];

      return allEntities
        .filter(item => item.name.toLowerCase().startsWith(query.toLowerCase()))
        .slice(0, 5);
    },
    render: () => {
      let component: any;
      let popup: any;

      return {
        onStart: (props: any) => {
          component = document.createElement('div');
          component.className = 'mention-suggestions bg-white border rounded-lg shadow-lg p-2 max-w-xs';

          popup = tippy('body', {
            getReferenceClientRect: props.clientRect,
            appendTo: () => document.body,
            content: component,
            showOnCreate: true,
            interactive: true,
            trigger: 'manual',
            placement: 'bottom-start',
          });
        },
        onUpdate(props: any) {
          component.innerHTML = '';

          if (props.items.length === 0) {
            const noResults = document.createElement('div');
            noResults.className = 'text-gray-500 text-sm p-2';
            noResults.textContent = 'No se encontraron entidades';
            component.appendChild(noResults);
            return;
          }

          props.items.forEach((item: any, index: number) => {
            const button = document.createElement('button');
            button.className = `block w-full text-left p-2 hover:bg-gray-100 rounded ${
              index === props.selectedIndex ? 'bg-gray-100' : ''
            }`;

            const icon = item.type === 'character' ? '👤' : '📍';
            button.innerHTML = `
              <div class="flex items-center gap-2">
                <span>${icon}</span>
                <span class="font-medium">${item.name}</span>
                <span class="text-xs text-gray-500">${item.type === 'character' ? 'Personaje' : 'Localización'}</span>
              </div>
            `;

            button.addEventListener('click', () => props.command({ id: item.id, label: item.name, type: item.type }));
            component.appendChild(button);
          });
        },
        onKeyDown(props: any) {
          if (props.event.key === 'Escape') {
            popup[0].hide();
            return true;
          }
          return false;
        },
        onExit() {
          popup[0].destroy();
        },
      };
    },
  };

  const editor = useEditor({
    extensions: [
      StarterKit.configure({
        bulletList: {
          keepMarks: true,
          keepAttributes: false,
        },
        orderedList: {
          keepMarks: true,
          keepAttributes: false,
        },
        // Disable strike and horizontalRule from StarterKit since we're adding them explicitly
        strike: false,
        horizontalRule: false,
      }),
      Placeholder.configure({
        placeholder,
      }),
      CharacterCount.configure({
        limit: 50000, // Límite de caracteres
      }),
      TextStyle,
      Color.configure({
        types: ['textStyle'],
      }),
      Highlight.configure({
        multicolor: true,
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      Strike,
      Subscript,
      Superscript,
      Underline,
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'text-blue-600 underline hover:text-blue-800 cursor-pointer',
        },
      }),
      HorizontalRule,
      Mention.configure({
        HTMLAttributes: {
          class: 'mention bg-blue-100 text-blue-800 px-1 py-0.5 rounded text-sm font-medium',
        },
        suggestion: mentionSuggestion,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
    ],
    content: value,
    onUpdate: ({ editor }) => {
      onChange(editor.getHTML());
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
          'prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground',
          'prose-em:text-foreground prose-code:text-foreground prose-pre:bg-muted',
          'prose-blockquote:text-muted-foreground prose-blockquote:border-border',
          'prose-ul:text-foreground prose-ol:text-foreground prose-li:text-foreground',
          'max-w-none p-4'
        ),
      },
    },
  });

  // Efecto de autoguardado
  useEffect(() => {
    if (!autoSave || !onAutoSave || !editor) return;

    const timer = setTimeout(() => {
      if (autoSaveStatus === 'idle') {
        setAutoSaveStatus('saving');
        try {
          onAutoSave(editor.getHTML());
          setAutoSaveStatus('saved');
          setLastSaved(new Date());
          setTimeout(() => setAutoSaveStatus('idle'), 2000);
        } catch (error) {
          setAutoSaveStatus('error');
          setTimeout(() => setAutoSaveStatus('idle'), 3000);
        }
      }
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [value, autoSave, onAutoSave, autoSaveDelay, autoSaveStatus, editor]);

  // Helper functions
  const setColor = useCallback((color: string) => {
    editor?.chain().focus().setColor(color).run();
    setShowColorPicker(false);
  }, [editor]);

  const setHighlight = useCallback((color: string) => {
    editor?.chain().focus().setHighlight({ color }).run();
    setShowHighlightPicker(false);
  }, [editor]);

  const setLink = useCallback(() => {
    if (linkUrl) {
      editor?.chain().focus().setLink({ href: linkUrl }).run();
    }
    setLinkUrl('');
    setShowLinkDialog(false);
  }, [editor, linkUrl]);

  const unsetLink = useCallback(() => {
    editor?.chain().focus().unsetLink().run();
  }, [editor]);

  const clearFormat = useCallback(() => {
    editor?.chain().focus().clearNodes().unsetAllMarks().run();
  }, [editor]);

  // Nuevas funciones helper para Fase 2
  const toggleFullscreen = useCallback(() => {
    setIsFullscreen(!isFullscreen);
  }, [isFullscreen]);

  const insertTable = useCallback(() => {
    editor?.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run();
  }, [editor]);

  const deleteTable = useCallback(() => {
    editor?.chain().focus().deleteTable().run();
  }, [editor]);

  const addColumnBefore = useCallback(() => {
    editor?.chain().focus().addColumnBefore().run();
  }, [editor]);

  const addColumnAfter = useCallback(() => {
    editor?.chain().focus().addColumnAfter().run();
  }, [editor]);

  const deleteColumn = useCallback(() => {
    editor?.chain().focus().deleteColumn().run();
  }, [editor]);

  const addRowBefore = useCallback(() => {
    editor?.chain().focus().addRowBefore().run();
  }, [editor]);

  const addRowAfter = useCallback(() => {
    editor?.chain().focus().addRowAfter().run();
  }, [editor]);

  const deleteRow = useCallback(() => {
    editor?.chain().focus().deleteRow().run();
  }, [editor]);

  const searchAndReplace = useCallback(() => {
    if (!searchTerm) return;

    const content = editor?.getHTML() || '';
    const regex = new RegExp(searchTerm, 'gi');
    const newContent = content.replace(regex, replaceTerm);

    editor?.commands.setContent(newContent);
    setShowSearchDialog(false);
    setSearchTerm('');
    setReplaceTerm('');
  }, [editor, searchTerm, replaceTerm]);

  if (!editor) {
    return null;
  }

  // Componente de estado de autoguardado
  const AutoSaveStatus = () => {
    if (!autoSave) return null;

    const getStatusText = () => {
      switch (autoSaveStatus) {
        case 'saving': return '💾 Guardando...';
        case 'saved': return '✅ Guardado';
        case 'error': return '❌ Error al guardar';
        default: return lastSaved ? `💾 Último guardado: ${lastSaved.toLocaleTimeString()}` : '';
      }
    };

    const getStatusColor = () => {
      switch (autoSaveStatus) {
        case 'saving': return 'text-blue-600';
        case 'saved': return 'text-green-600';
        case 'error': return 'text-red-600';
        default: return 'text-gray-500';
      }
    };

    return (
      <div className={`text-xs ${getStatusColor()} flex items-center gap-1`}>
        {getStatusText()}
      </div>
    );
  };

  const ToolbarButton = ({ 
    onClick, 
    isActive = false, 
    disabled = false, 
    children, 
    title 
  }: {
    onClick: () => void;
    isActive?: boolean;
    disabled?: boolean;
    children: React.ReactNode;
    title: string;
  }) => (
    <Button
      type="button"
      variant={isActive ? "default" : "ghost"}
      size="sm"
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={cn(
        "h-8 w-8 p-0",
        isActive && "bg-primary text-primary-foreground"
      )}
    >
      {children}
    </Button>
  );

  // Color palette for text and highlight
  const colors = [
    '#000000', '#374151', '#6B7280', '#9CA3AF',
    '#EF4444', '#F97316', '#EAB308', '#22C55E',
    '#3B82F6', '#8B5CF6', '#EC4899', '#F43F5E'
  ];

  const highlightColors = [
    '#FEF3C7', '#FED7AA', '#FECACA', '#D1FAE5',
    '#DBEAFE', '#E0E7FF', '#F3E8FF', '#FCE7F3'
  ];

  return (
    <div className={cn(
      "border rounded-lg overflow-hidden",
      isFullscreen && "fixed inset-0 z-50 bg-white rounded-none",
      className
    )}>
      {/* Toolbar */}
      <div className="border-b bg-muted/30 p-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-1 flex-wrap">
          {/* Text Formatting */}
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBold().run()}
            isActive={editor.isActive('bold')}
            title="Negrita (Ctrl+B)"
          >
            <Bold className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleItalic().run()}
            isActive={editor.isActive('italic')}
            title="Cursiva (Ctrl+I)"
          >
            <Italic className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleUnderline().run()}
            isActive={editor.isActive('underline')}
            title="Subrayado (Ctrl+U)"
          >
            <UnderlineIcon className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleStrike().run()}
            isActive={editor.isActive('strike')}
            title="Tachado"
          >
            <Strikethrough className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleSubscript().run()}
            isActive={editor.isActive('subscript')}
            title="Subíndice"
          >
            <SubscriptIcon className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleSuperscript().run()}
            isActive={editor.isActive('superscript')}
            title="Superíndice"
          >
            <SuperscriptIcon className="h-4 w-4" />
          </ToolbarButton>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Colors and Highlighting */}
          <div className="relative">
            <ToolbarButton
              onClick={() => setShowColorPicker(!showColorPicker)}
              title="Color de texto"
            >
              <Palette className="h-4 w-4" />
            </ToolbarButton>
            {showColorPicker && (
              <div className="absolute top-10 left-0 z-50 bg-white border rounded-lg shadow-lg p-2 grid grid-cols-4 gap-1">
                {colors.map((color) => (
                  <button
                    key={color}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => setColor(color)}
                    title={`Color ${color}`}
                  />
                ))}
              </div>
            )}
          </div>

          <div className="relative">
            <ToolbarButton
              onClick={() => setShowHighlightPicker(!showHighlightPicker)}
              isActive={editor.isActive('highlight')}
              title="Resaltar texto"
            >
              <Highlighter className="h-4 w-4" />
            </ToolbarButton>
            {showHighlightPicker && (
              <div className="absolute top-10 left-0 z-50 bg-white border rounded-lg shadow-lg p-2 grid grid-cols-4 gap-1">
                {highlightColors.map((color) => (
                  <button
                    key={color}
                    className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform"
                    style={{ backgroundColor: color }}
                    onClick={() => setHighlight(color)}
                    title={`Resaltar con ${color}`}
                  />
                ))}
                <button
                  className="w-6 h-6 rounded border border-gray-300 hover:scale-110 transition-transform bg-white flex items-center justify-center"
                  onClick={() => editor.chain().focus().unsetHighlight().run()}
                  title="Quitar resaltado"
                >
                  ×
                </button>
              </div>
            )}
          </div>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Headings */}
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 1 }).run()}
            isActive={editor.isActive('heading', { level: 1 })}
            title="Título 1"
          >
            <Type className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleHeading({ level: 2 }).run()}
            isActive={editor.isActive('heading', { level: 2 })}
            title="Título 2"
          >
            <Type className="h-3 w-3" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().setParagraph().run()}
            isActive={editor.isActive('paragraph')}
            title="Párrafo normal"
          >
            <AlignLeft className="h-4 w-4" />
          </ToolbarButton>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Text Alignment */}
          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('left').run()}
            isActive={editor.isActive({ textAlign: 'left' })}
            title="Alinear a la izquierda"
          >
            <AlignLeft className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('center').run()}
            isActive={editor.isActive({ textAlign: 'center' })}
            title="Centrar"
          >
            <AlignCenter className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('right').run()}
            isActive={editor.isActive({ textAlign: 'right' })}
            title="Alinear a la derecha"
          >
            <AlignRight className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().setTextAlign('justify').run()}
            isActive={editor.isActive({ textAlign: 'justify' })}
            title="Justificar"
          >
            <AlignJustify className="h-4 w-4" />
          </ToolbarButton>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Lists */}
          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBulletList().run()}
            isActive={editor.isActive('bulletList')}
            title="Lista con viñetas"
          >
            <List className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleOrderedList().run()}
            isActive={editor.isActive('orderedList')}
            title="Lista numerada"
          >
            <ListOrdered className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().toggleBlockquote().run()}
            isActive={editor.isActive('blockquote')}
            title="Cita"
          >
            <Quote className="h-4 w-4" />
          </ToolbarButton>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Links and Additional Tools */}
          <div className="relative">
            <ToolbarButton
              onClick={() => {
                if (editor.isActive('link')) {
                  unsetLink();
                } else {
                  setShowLinkDialog(true);
                }
              }}
              isActive={editor.isActive('link')}
              title={editor.isActive('link') ? "Quitar enlace" : "Añadir enlace"}
            >
              <LinkIcon className="h-4 w-4" />
            </ToolbarButton>
            {showLinkDialog && (
              <div className="absolute top-10 left-0 z-50 bg-white border rounded-lg shadow-lg p-3 w-64">
                <div className="flex gap-2">
                  <input
                    type="url"
                    placeholder="https://ejemplo.com"
                    value={linkUrl}
                    onChange={(e) => setLinkUrl(e.target.value)}
                    className="flex-1 px-2 py-1 border rounded text-sm"
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        setLink();
                      } else if (e.key === 'Escape') {
                        setShowLinkDialog(false);
                        setLinkUrl('');
                      }
                    }}
                    autoFocus
                  />
                  <Button
                    size="sm"
                    onClick={setLink}
                    disabled={!linkUrl}
                  >
                    OK
                  </Button>
                </div>
              </div>
            )}
          </div>

          <ToolbarButton
            onClick={() => editor.chain().focus().setHorizontalRule().run()}
            title="Insertar línea horizontal"
          >
            <Minus className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={clearFormat}
            title="Limpiar formato"
          >
            <Eraser className="h-4 w-4" />
          </ToolbarButton>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Undo/Redo */}
          <ToolbarButton
            onClick={() => editor.chain().focus().undo().run()}
            disabled={!editor.can().undo()}
            title="Deshacer (Ctrl+Z)"
          >
            <Undo className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={() => editor.chain().focus().redo().run()}
            disabled={!editor.can().redo()}
            title="Rehacer (Ctrl+Y)"
          >
            <Redo className="h-4 w-4" />
          </ToolbarButton>

          <Separator orientation="vertical" className="h-6 mx-1" />

          {/* Nuevas herramientas - Fase 2 */}
          <ToolbarButton
            onClick={() => setShowSearchDialog(true)}
            title="Buscar y reemplazar"
          >
            <Search className="h-4 w-4" />
          </ToolbarButton>

          <ToolbarButton
            onClick={insertTable}
            title="Insertar tabla"
          >
            <TableIcon className="h-4 w-4" />
          </ToolbarButton>

          {editor.isActive('table') && (
            <>
              <ToolbarButton
                onClick={addColumnAfter}
                title="Añadir columna"
              >
                <Plus className="h-3 w-3" />
              </ToolbarButton>

              <ToolbarButton
                onClick={deleteTable}
                title="Eliminar tabla"
              >
                <Trash2 className="h-4 w-4" />
              </ToolbarButton>
            </>
          )}
          </div>

          {/* Controles de la derecha */}
          <div className="flex items-center gap-2">
            <AutoSaveStatus />

            <ToolbarButton
              onClick={toggleFullscreen}
              title={isFullscreen ? "Salir de pantalla completa" : "Pantalla completa"}
            >
              {isFullscreen ? <Minimize className="h-4 w-4" /> : <Maximize className="h-4 w-4" />}
            </ToolbarButton>
          </div>
        </div>
      </div>

      {/* Editor Content */}
      <div
        className={cn(
          "bg-background",
          isFullscreen && "flex-1 overflow-auto"
        )}
        style={{ minHeight: isFullscreen ? 'auto' : minHeight }}
      >
        <EditorContent editor={editor} />
      </div>

      {/* Diálogo de Búsqueda y Reemplazo */}
      <Dialog open={showSearchDialog} onOpenChange={setShowSearchDialog}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Buscar y Reemplazar</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <label className="text-sm font-medium">Buscar:</label>
              <Input
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Texto a buscar..."
                className="mt-1"
              />
            </div>
            <div>
              <label className="text-sm font-medium">Reemplazar con:</label>
              <Input
                value={replaceTerm}
                onChange={(e) => setReplaceTerm(e.target.value)}
                placeholder="Texto de reemplazo..."
                className="mt-1"
              />
            </div>
            <div className="flex gap-2 justify-end">
              <Button
                variant="outline"
                onClick={() => setShowSearchDialog(false)}
              >
                Cancelar
              </Button>
              <Button
                onClick={searchAndReplace}
                disabled={!searchTerm}
              >
                Reemplazar Todo
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>

      {/* Footer with word count */}
      {showWordCount && (
        <div className="border-t bg-muted/30 px-4 py-2 text-xs text-muted-foreground flex justify-between items-center">
          <div className="flex items-center gap-2">
            <FileText className="h-3 w-3" />
            <span>Contenido de la escena</span>
          </div>
          <div className="flex gap-4">
            <span>
              Palabras: {editor.storage.characterCount.words()}
            </span>
            <span>
              Caracteres: {editor.storage.characterCount.characters()}
            </span>
          </div>
        </div>
      )}
    </div>
  );
};

export default RichTextEditor;
