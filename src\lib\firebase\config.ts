
import { initializeApp } from "firebase/app";
import { getAuth } from "firebase/auth";
import {
  getFirestore,
  connectFirestoreEmulator,
  initializeFirestore,
  persistentLocalCache,
  persistentMultipleTabManager,
  CACHE_SIZE_UNLIMITED
} from "firebase/firestore";
import { getAnalytics } from "firebase/analytics";

// Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyC7ATpPqDHoWck4xMOG9Sfvp6HlCGPmXVk",
  authDomain: "scribehub-v1.firebaseapp.com",
  projectId: "scribehub-v1",
  storageBucket: "scribehub-v1.firebasestorage.app",
  messagingSenderId: "597217346847",
  appId: "1:597217346847:web:0c450f5f255d38572b9c2a",
  measurementId: "G-2VBBHWKN72"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);
export const auth = getAuth(app);

// Initialize Firestore with optimized settings and persistent cache
// This approach replaces the deprecated enableIndexedDbPersistence method
let firestoreInstance;

try {
  // Inicializar Firestore con persistencia local y soporte para múltiples pestañas
  firestoreInstance = initializeFirestore(app, {
    // Usar caché local persistente con tamaño ilimitado
    cache: persistentLocalCache({
      // Permitir que múltiples pestañas accedan a la misma base de datos
      tabManager: persistentMultipleTabManager(),
      // Establecer tamaño de caché ilimitado
      cacheSizeBytes: CACHE_SIZE_UNLIMITED
    })
  });

  console.log("Firestore initialized with persistent cache and multi-tab support");
} catch (error) {
  // Si falla la inicialización con persistencia, usar la configuración estándar
  console.warn("Error initializing Firestore with persistence, falling back to default:", error);
  firestoreInstance = getFirestore(app);
}

// Exportar la instancia de Firestore
export const db = firestoreInstance;

// Initialize Analytics
export const analytics = getAnalytics(app);

export default app;
