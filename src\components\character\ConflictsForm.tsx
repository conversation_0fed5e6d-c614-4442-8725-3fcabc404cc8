import { TextAreaField } from './FormFields';
import { Character } from '@/types/firestore-models';
import { AlertCircle } from 'lucide-react';

interface ConflictsFormProps {
  character: Character | Omit<Character, 'id'>;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
}

const ConflictsForm = ({ 
  character, 
  onInputChange
}: ConflictsFormProps) => {
  return (
    <div className="space-y-6">
      <div className="bg-secondary/30 p-4 rounded-md mb-6">
        <div className="flex items-start gap-2">
          <AlertCircle className="h-5 w-5 text-primary mt-0.5" />
          <div>
            <h3 className="text-sm font-medium mb-1">Sobre los conflictos del personaje:</h3>
            <p className="text-sm text-muted-foreground">
              Los personajes memorables tienen conflictos tanto internos como externos. 
              El conflicto interno es la lucha psicológica o emocional dentro del personaje, 
              mientras que el conflicto externo es el obstáculo o antagonista que enfrenta en el mundo exterior.
            </p>
          </div>
        </div>
      </div>
      
      <TextAreaField
        id="internal_conflict"
        name="internal_conflict"
        label="Conflicto interno"
        value={character.internal_conflict || ''}
        onChange={onInputChange}
        placeholder="¿Qué lucha interna, dilema moral o contradicción enfrenta el personaje?"
        minHeight="120px"
      />
      
      <TextAreaField
        id="external_conflict"
        name="external_conflict"
        label="Conflicto externo"
        value={character.external_conflict || ''}
        onChange={onInputChange}
        placeholder="¿Qué obstáculos, antagonistas o fuerzas externas se oponen al personaje?"
        minHeight="120px"
      />
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8">
        <div className="bg-secondary/20 p-4 rounded-md">
          <h3 className="text-sm font-medium mb-2">Ejemplos de conflictos internos:</h3>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
            <li>Lealtad dividida entre dos grupos o personas</li>
            <li>Lucha entre el deber y los deseos personales</li>
            <li>Culpa por acciones pasadas</li>
            <li>Miedo al fracaso o al éxito</li>
            <li>Crisis de identidad o de fe</li>
          </ul>
        </div>
        
        <div className="bg-secondary/20 p-4 rounded-md">
          <h3 className="text-sm font-medium mb-2">Ejemplos de conflictos externos:</h3>
          <ul className="text-sm text-muted-foreground space-y-1 list-disc pl-5">
            <li>Enfrentamiento con un antagonista</li>
            <li>Lucha contra la naturaleza o un desastre</li>
            <li>Oposición de la sociedad o instituciones</li>
            <li>Escasez de recursos o tiempo</li>
            <li>Obstáculos físicos o limitaciones</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default ConflictsForm;
