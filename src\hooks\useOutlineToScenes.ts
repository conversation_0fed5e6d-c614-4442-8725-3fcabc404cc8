import { useState } from 'react';
import { useToast } from '@/hooks/use-toast';
import { Outline } from '@/types/outline';
import { Scene } from '@/types/firestore-models';
import { createScenesFromOutline } from '@/lib/firebase/firestore/outline-scene-utils';
import { getChapterRef } from '@/lib/firebase/firestore/chapters';

interface UseOutlineToScenesProps {
  projectId: string;
}

export const useOutlineToScenes = ({ projectId }: UseOutlineToScenesProps) => {
  const { toast } = useToast();
  const [isConverting, setIsConverting] = useState(false);

  /**
   * Convierte todas las escenas de un outline a escenas de Firestore
   */
  const convertOutlineToScenes = async (outline: Outline, chapterId: string): Promise<Scene[]> => {
    try {
      setIsConverting(true);
      
      if (!outline.scenes || outline.scenes.length === 0) {
        toast({
          title: "Sin escenas",
          description: "El outline no contiene escenas para convertir.",
          variant: "destructive",
        });
        return [];
      }

      // Obtener la referencia al capítulo
      const chapterRef = getChapterRef(projectId, chapterId);
      
      // Convertir las escenas del outline a escenas de Firestore
      const scenes = await createScenesFromOutline(projectId, outline.scenes, chapterRef);
      
      toast({
        title: "Conversión completada",
        description: `Se han creado ${scenes.length} escenas a partir del outline "${outline.title}".`,
      });
      
      return scenes;
    } catch (error) {
      console.error("Error al convertir outline a escenas:", error);
      toast({
        title: "Error en la conversión",
        description: "No se pudieron crear las escenas a partir del outline.",
        variant: "destructive",
      });
      return [];
    } finally {
      setIsConverting(false);
    }
  };

  return {
    convertOutlineToScenes,
    isConverting
  };
};