import { 
  collection, 
  getDocs, 
  query, 
  where,
  serverTimestamp
} from "firebase/firestore";
import { db } from "../config";

// Import old data models
import { Project as OldProject } from "@/types/project";
import { Character as <PERSON><PERSON><PERSON><PERSON> } from "@/types/character";
import { Outline as OldOutline, OutlineScene } from "@/types/outline";

// Import new data models
import { 
  Project, 
  Character, 
  Chapter, 
  Scene 
} from "@/types/firestore-models";

// Import new Firestore services
import { 
  createProject, 
  create<PERSON>haracter, 
  createChapter, 
  createScene,
  createSceneDataFromOutlineScene,
  getChapterRef
} from "../firestore";

/**
 * Migrate a project and all its related data from the old structure to the new structure
 */
export const migrateProject = async (oldProjectId: string): Promise<{ success: boolean, newProjectId: string }> => {
  try {
    // Get the old project data
    const oldProjectRef = collection(db, "projects");
    const oldProjectQuery = query(oldProjectRef, where("id", "==", oldProjectId));
    const oldProjectSnapshot = await getDocs(oldProjectQuery);
    
    if (oldProjectSnapshot.empty) {
      throw new Error("Project not found");
    }
    
    const oldProjectData = oldProjectSnapshot.docs[0].data() as OldProject;
    
    // Create the new project
    const newProjectData: Omit<Project, 'id' | 'createdAt' | 'updatedAt'> = {
      title: oldProjectData.title,
      genre: oldProjectData.genre,
      logline: "",
      theme: "",
      target_audience: "",
      overall_status: "Idea",
      word_count_total: 0,
      target_word_count: 0,
      userId: oldProjectData.userId
    };
    
    const newProject = await createProject(newProjectData);
    
    // Migrate characters
    await migrateCharacters(oldProjectId, newProject.id);
    
    // Migrate outlines to chapters and scenes
    await migrateOutlines(oldProjectId, newProject.id);
    
    return { success: true, newProjectId: newProject.id };
  } catch (error) {
    console.error("Error migrating project:", error);
    throw error;
  }
};

/**
 * Migrate characters from the old structure to the new structure
 */
const migrateCharacters = async (oldProjectId: string, newProjectId: string): Promise<void> => {
  try {
    // Get all characters for the old project
    const oldCharactersRef = collection(db, "characters");
    const oldCharactersQuery = query(oldCharactersRef, where("projectId", "==", oldProjectId));
    const oldCharactersSnapshot = await getDocs(oldCharactersQuery);
    
    // Create each character in the new structure
    const characterPromises = oldCharactersSnapshot.docs.map(async (characterDoc) => {
      const oldCharacterData = characterDoc.data() as OldCharacter;
      
      const newCharacterData: Omit<Character, 'id' | 'createdAt' | 'updatedAt'> = {
        name: oldCharacterData.name,
        alias: [],
        role: "",
        descripcion_fisica: oldCharacterData.physicalDescription,
        personalidad: [oldCharacterData.personality],
        traits: [],
        motivacion: oldCharacterData.motivation,
        internal_conflict: "",
        external_conflict: "",
        bio: oldCharacterData.background,
        arc_summary: "",
        initial_state: "",
        final_state: "",
        notas_adicionales: ""
      };
      
      await createCharacter(newProjectId, newCharacterData);
    });
    
    await Promise.all(characterPromises);
  } catch (error) {
    console.error("Error migrating characters:", error);
    throw error;
  }
};

/**
 * Migrate outlines from the old structure to chapters and scenes in the new structure
 */
const migrateOutlines = async (oldProjectId: string, newProjectId: string): Promise<void> => {
  try {
    // Get all outlines for the old project
    const oldOutlinesRef = collection(db, "outlines");
    const oldOutlinesQuery = query(oldOutlinesRef, where("projectId", "==", oldProjectId));
    const oldOutlinesSnapshot = await getDocs(oldOutlinesQuery);
    
    // Process each outline
    const outlinePromises = oldOutlinesSnapshot.docs.map(async (outlineDoc) => {
      const oldOutlineData = outlineDoc.data() as OldOutline;
      
      // Create a chapter for the outline
      const newChapterData: Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'> = {
        title: oldOutlineData.title,
        order: 0, // Assign a default order
        summary: oldOutlineData.description || "",
        status: "Borrador",
        word_count: 0,
        pov_characters: [],
        scene_order: []
      };
      
      const newChapter = await createChapter(newProjectId, newChapterData);
      
      // Create scenes for each outline scene
      if (oldOutlineData.scenes && oldOutlineData.scenes.length > 0) {
        const scenePromises = oldOutlineData.scenes.map(async (oldScene: OutlineScene, index: number) => {
          // Usar la nueva función de utilidad para convertir OutlineScene a SceneWithoutId
          const chapterRef = getChapterRef(newProjectId, newChapter.id);
          const newSceneData = createSceneDataFromOutlineScene(newProjectId, oldScene, chapterRef);
          
          // Asegurar que el orden global se establece correctamente
          newSceneData.global_order = index;
          
          await createScene(newProjectId, newSceneData);
        });
        
        await Promise.all(scenePromises);
      }
    });
    
    await Promise.all(outlinePromises);
  } catch (error) {
    console.error("Error migrating outlines:", error);
    throw error;
  }
};
