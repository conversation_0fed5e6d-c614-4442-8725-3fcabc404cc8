import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  query,
  where,
  serverTimestamp,
  getDoc,
  DocumentReference,
  getDocsFromCache,
  getDocFromCache
} from "firebase/firestore";
import { db } from "../config";
import { Project, ProjectWithoutId } from "@/types/firestore-models";
import { handleFirestoreError } from "../connection-manager";

// Collection reference
export const projectsCollection = collection(db, "projects");

/**
 * Create a new project
 */
export const createProject = async (projectData: ProjectWithoutId): Promise<Project> => {
  try {
    const docRef = await addDoc(projectsCollection, {
      ...projectData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      id: docRef.id,
      ...projectData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating project:", error);
    throw error;
  }
};

/**
 * Get all projects for a user
 * Intenta obtener los proyectos desde el servidor, con fallback a caché local
 * y manejo de reconexión automática
 */
export const getUserProjects = async (userId: string): Promise<Project[]> => {
  try {
    const q = query(projectsCollection, where("userId", "==", userId));
    let querySnapshot;

    try {
      // Intentar obtener desde el servidor
      querySnapshot = await getDocs(q);
    } catch (serverError) {
      console.warn("Error fetching projects from server, trying cache:", serverError);

      try {
        // Intentar obtener desde la caché si falla el servidor
        querySnapshot = await getDocsFromCache(q);
        console.log("Retrieved projects from cache");
      } catch (cacheError) {
        console.error("Error fetching projects from cache:", cacheError);

        // Intentar reconectar si es un error de red
        const reconnected = await handleFirestoreError(serverError);

        if (reconnected) {
          // Reintentar después de reconectar
          querySnapshot = await getDocs(q);
        } else {
          // Si no se pudo reconectar, propagar el error original
          throw serverError;
        }
      }
    }

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Project[];
  } catch (error) {
    console.error("Error fetching user projects:", error);
    throw error;
  }
};

/**
 * Get a project by ID
 * Intenta obtener el proyecto desde el servidor, con fallback a caché local
 * y manejo de reconexión automática
 */
export const getProjectById = async (projectId: string): Promise<Project> => {
  try {
    const projectRef = doc(db, "projects", projectId);
    let projectSnapshot;

    try {
      // Intentar obtener desde el servidor
      projectSnapshot = await getDoc(projectRef);
    } catch (serverError) {
      console.warn("Error fetching project from server, trying cache:", serverError);

      try {
        // Intentar obtener desde la caché si falla el servidor
        projectSnapshot = await getDocFromCache(projectRef);
        console.log("Retrieved project from cache");
      } catch (cacheError) {
        console.error("Error fetching project from cache:", cacheError);

        // Intentar reconectar si es un error de red
        const reconnected = await handleFirestoreError(serverError);

        if (reconnected) {
          // Reintentar después de reconectar
          projectSnapshot = await getDoc(projectRef);
        } else {
          // Si no se pudo reconectar, propagar el error original
          throw serverError;
        }
      }
    }

    if (projectSnapshot.exists()) {
      return {
        id: projectSnapshot.id,
        ...projectSnapshot.data()
      } as Project;
    } else {
      throw new Error("Project not found");
    }
  } catch (error) {
    console.error("Error fetching project:", error);
    throw error;
  }
};

/**
 * Update a project
 */
export const updateProject = async (projectId: string, data: Partial<Project>): Promise<Partial<Project>> => {
  try {
    const projectRef = doc(db, "projects", projectId);

    const updateData = {
      ...data,
      updatedAt: serverTimestamp()
    };

    await updateDoc(projectRef, updateData);

    return {
      id: projectId,
      ...data
    };
  } catch (error) {
    console.error("Error updating project:", error);
    throw error;
  }
};

/**
 * Delete a project and all its subcollections
 */
export const deleteProject = async (projectId: string): Promise<{ success: boolean }> => {
  try {
    // Get reference to the project document
    const projectRef = doc(db, "projects", projectId);

    // Delete all subcollections
    // This will be implemented in each subcollection's service
    // For example: await deleteAllProjectCharacters(projectId);

    // Delete the project document
    await deleteDoc(projectRef);

    return { success: true };
  } catch (error) {
    console.error("Error deleting project:", error);
    throw error;
  }
};

/**
 * Get a reference to a project document
 */
export const getProjectRef = (projectId: string): DocumentReference => {
  return doc(db, "projects", projectId);
};
