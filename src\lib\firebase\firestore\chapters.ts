import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  getDoc,
  query, 
  orderBy,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { Chapter, ChapterWithoutId } from "@/types/firestore-models";

/**
 * Get the chapters subcollection for a project
 */
export const getChaptersCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "chapters");
};

/**
 * Create a new chapter in a project
 */
export const createChapter = async (
  projectId: string, 
  chapterData: ChapterWithoutId
): Promise<Chapter> => {
  try {
    const chaptersCollection = getChaptersCollection(projectId);
    
    const docRef = await addDoc(chaptersCollection, {
      ...chapterData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return { 
      id: docRef.id, 
      ...chapterData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating chapter:", error);
    throw error;
  }
};

/**
 * Get all chapters for a project, ordered by their order field
 */
export const getProjectChapters = async (projectId: string): Promise<Chapter[]> => {
  try {
    const chaptersCollection = getChaptersCollection(projectId);
    const q = query(chaptersCollection, orderBy("order", "asc"));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Chapter[];
  } catch (error) {
    console.error("Error fetching project chapters:", error);
    throw error;
  }
};

/**
 * Get a chapter by ID
 */
export const getChapterById = async (projectId: string, chapterId: string): Promise<Chapter> => {
  try {
    const chapterRef = doc(db, "projects", projectId, "chapters", chapterId);
    const chapterSnap = await getDoc(chapterRef);
    
    if (chapterSnap.exists()) {
      return { 
        id: chapterSnap.id, 
        ...chapterSnap.data() 
      } as Chapter;
    } else {
      throw new Error("Chapter not found");
    }
  } catch (error) {
    console.error("Error fetching chapter:", error);
    throw error;
  }
};

/**
 * Update a chapter
 */
export const updateChapter = async (
  projectId: string,
  chapterId: string, 
  chapterData: Partial<Chapter>
): Promise<Partial<Chapter>> => {
  try {
    const chapterRef = doc(db, "projects", projectId, "chapters", chapterId);
    
    const updateData = {
      ...chapterData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(chapterRef, updateData);
    
    return { 
      id: chapterId, 
      ...chapterData 
    };
  } catch (error) {
    console.error("Error updating chapter:", error);
    throw error;
  }
};

/**
 * Delete a chapter
 */
export const deleteChapter = async (projectId: string, chapterId: string): Promise<{ success: boolean }> => {
  try {
    const chapterRef = doc(db, "projects", projectId, "chapters", chapterId);
    await deleteDoc(chapterRef);
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting chapter:", error);
    throw error;
  }
};

/**
 * Delete all chapters for a project (used when deleting a project)
 */
export const deleteAllProjectChapters = async (projectId: string): Promise<void> => {
  try {
    const chaptersCollection = getChaptersCollection(projectId);
    const querySnapshot = await getDocs(chaptersCollection);
    
    const batch = writeBatch(db);
    
    querySnapshot.docs.forEach(chapterDoc => {
      const chapterRef = doc(db, "projects", projectId, "chapters", chapterDoc.id);
      batch.delete(chapterRef);
    });
    
    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project chapters:", error);
    throw error;
  }
};

/**
 * Get a reference to a chapter document
 */
export const getChapterRef = (projectId: string, chapterId: string): DocumentReference => {
  return doc(db, "projects", projectId, "chapters", chapterId);
};
