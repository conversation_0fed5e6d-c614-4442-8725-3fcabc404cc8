
import { Card, CardContent, CardHeader } from '@/components/ui/card';

interface CharacterAttributesProps {
  strength: number;
  intelligence: number;
  charisma: number;
  resilience: number;
}

const CharacterAttributes = ({ strength, intelligence, charisma, resilience }: CharacterAttributesProps) => {
  return (
    <Card className="mt-4">
      <CardHeader>
        <h3 className="text-lg font-semibold">Atributos</h3>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <div className="flex items-center justify-between mb-1">
            <span>Fuerza</span>
            <span className="font-medium">{strength || 0}%</span>
          </div>
          <div className="w-full bg-secondary h-2 rounded-full">
            <div 
              className="bg-primary h-2 rounded-full" 
              style={{ width: `${strength || 0}%` }}
            />
          </div>
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-1">
            <span>Inteligencia</span>
            <span className="font-medium">{intelligence || 0}%</span>
          </div>
          <div className="w-full bg-secondary h-2 rounded-full">
            <div 
              className="bg-primary h-2 rounded-full" 
              style={{ width: `${intelligence || 0}%` }}
            />
          </div>
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-1">
            <span>Carisma</span>
            <span className="font-medium">{charisma || 0}%</span>
          </div>
          <div className="w-full bg-secondary h-2 rounded-full">
            <div 
              className="bg-primary h-2 rounded-full" 
              style={{ width: `${charisma || 0}%` }}
            />
          </div>
        </div>
        
        <div>
          <div className="flex items-center justify-between mb-1">
            <span>Resiliencia</span>
            <span className="font-medium">{resilience || 0}%</span>
          </div>
          <div className="w-full bg-secondary h-2 rounded-full">
            <div 
              className="bg-primary h-2 rounded-full" 
              style={{ width: `${resilience || 0}%` }}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default CharacterAttributes;
