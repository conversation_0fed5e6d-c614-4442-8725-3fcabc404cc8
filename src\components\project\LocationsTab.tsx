import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Plus, Edit, Trash2, MapPin, Eye } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { Location } from '@/types/firestore-models';
import { deleteLocation } from '@/lib/firebase/firestore/locations';
import LocationCreator from '@/components/location/LocationCreator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';

interface LocationsTabProps {
  locations: Location[];
  projectId: string;
  onLocationUpdated: (location: Location) => void;
  onDeleteLocation: (locationId: string) => void;
  isDeleting: boolean;
}

const LocationsTab = ({
  locations,
  projectId,
  onLocationUpdated,
  onDeleteLocation,
  isDeleting
}: LocationsTabProps) => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [showLocationCreator, setShowLocationCreator] = useState(false);
  const [locationToEdit, setLocationToEdit] = useState<Location | null>(null);

  const handleCreateNew = () => {
    setLocationToEdit(null);
    setShowLocationCreator(true);
  };

  const handleEdit = (location: Location) => {
    setLocationToEdit(location);
    setShowLocationCreator(true);
  };

  const handleLocationCreated = (location: Location) => {
    onLocationUpdated(location);
    setShowLocationCreator(false);
    toast({
      title: "Localización creada",
      description: "La localización ha sido creada correctamente",
    });
  };

  const handleLocationUpdated = (location: Location) => {
    onLocationUpdated(location);
    setShowLocationCreator(false);
    setLocationToEdit(null);
    toast({
      title: "Localización actualizada",
      description: "La localización ha sido actualizada correctamente",
    });
  };

  const handleCancel = () => {
    setShowLocationCreator(false);
    setLocationToEdit(null);
  };

  const handleDelete = async (locationId: string) => {
    try {
      await deleteLocation(projectId, locationId);
      onDeleteLocation(locationId);
      toast({
        title: "Localización eliminada",
        description: "La localización ha sido eliminada correctamente",
      });
    } catch (error) {
      console.error("Error deleting location:", error);
      toast({
        title: "Error al eliminar",
        description: "No se pudo eliminar la localización. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    }
  };

  if (showLocationCreator) {
    return (
      <LocationCreator
        projectId={projectId}
        existingLocation={locationToEdit}
        onLocationCreated={handleLocationCreated}
        onLocationUpdated={handleLocationUpdated}
        onCancel={handleCancel}
      />
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <div>
          <h2 className="text-2xl font-bold">Localizaciones</h2>
          <p className="text-muted-foreground">
            Gestiona los lugares de tu historia
          </p>
        </div>
        <Button onClick={handleCreateNew}>
          <Plus className="mr-2 h-4 w-4" />
          Nueva Localización
        </Button>
      </div>

      {locations.length === 0 ? (
        <div className="text-center p-8 border rounded-lg bg-muted">
          <p className="text-muted-foreground mb-4">
            No hay localizaciones creadas para este proyecto.
          </p>
          <Button onClick={handleCreateNew}>
            <Plus className="mr-2 h-4 w-4" />
            Crear Localización
          </Button>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {locations.map((location) => (
            <Card key={location.id}>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MapPin className="h-5 w-5 text-primary" />
                  {location.name}
                </CardTitle>
                <CardDescription>
                  {location.type || "Sin tipo"}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="line-clamp-2 text-muted-foreground">
                  {location.description || "Sin descripción"}
                </p>
                {location.ubicacion_general && (
                  <p className="text-xs text-muted-foreground mt-2">
                    <span className="font-medium">Ubicación:</span> {location.ubicacion_general}
                  </p>
                )}
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(location)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  Editar
                </Button>
                <div className="flex gap-2">
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => navigate(`/projects/${projectId}/locations/${location.id}`)}
                    title="Ver detalles completos de la localización"
                  >
                    <Eye className="h-4 w-4 mr-2" />
                    Ver detalles
                  </Button>
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="destructive"
                        size="sm"
                        disabled={isDeleting}
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        Eliminar
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>¿Estás seguro?</AlertDialogTitle>
                        <AlertDialogDescription>
                          Esta acción no se puede deshacer. Se eliminará permanentemente la localización "{location.name}".
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction onClick={() => handleDelete(location.id)}>
                          Eliminar
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default LocationsTab;
