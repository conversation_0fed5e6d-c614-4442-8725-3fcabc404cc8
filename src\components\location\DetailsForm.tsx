import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';

interface DetailsFormProps {
  location: {
    description: string;
    importance_to_story: string;
    notas_adicionales: string;
  };
  onInputChange: (e: React.ChangeEvent<HTMLTextAreaElement>) => void;
}

const DetailsForm = ({ location, onInputChange }: DetailsFormProps) => {
  return (
    <div className="space-y-4">
      {/* Description */}
      <div>
        <Label htmlFor="description">Descripción detallada</Label>
        <Textarea
          id="description"
          name="description"
          value={location.description}
          onChange={onInputChange}
          placeholder="Describe esta localización con detalle"
          className="min-h-[120px]"
        />
      </div>

      {/* Importance to Story */}
      <div>
        <Label htmlFor="importance_to_story">Importancia para la historia</Label>
        <Textarea
          id="importance_to_story"
          name="importance_to_story"
          value={location.importance_to_story}
          onChange={onInputChange}
          placeholder="¿Por qué es relevante esta localización para tu narrativa?"
          className="min-h-[100px]"
        />
      </div>

      {/* Additional Notes */}
      <div>
        <Label htmlFor="notas_adicionales">Notas adicionales</Label>
        <Textarea
          id="notas_adicionales"
          name="notas_adicionales"
          value={location.notas_adicionales}
          onChange={onInputChange}
          placeholder="Cualquier otra información relevante sobre esta localización"
          className="min-h-[100px]"
        />
      </div>
    </div>
  );
};

export default DetailsForm;
