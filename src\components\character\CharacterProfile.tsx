
import { useState } from 'react';
import { Card, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ImageModal } from '@/components/ui/image-modal';

interface CharacterProfileProps {
  name: string;
  occupation?: string;
  age?: string;
  profilePicture?: string;
  image_url?: string;
  role?: string;
  color_tag?: string;
  alias?: string[];
}

const CharacterProfile = ({
  name,
  occupation,
  age,
  profilePicture,
  image_url,
  role,
  color_tag,
  alias = []
}: CharacterProfileProps) => {
  // Use new image_url field if available, fall back to legacy profilePicture
  const imageSource = image_url || profilePicture;
  const [isImageModalOpen, setIsImageModalOpen] = useState(false);

  const handleImageClick = () => {
    if (imageSource) {
      setIsImageModalOpen(true);
    }
  };

  return (
    <Card>
      <div
        className="aspect-square bg-secondary/30 flex items-center justify-center"
        style={color_tag ? { borderTop: `4px solid ${color_tag}` } : undefined}
      >
        {imageSource ? (
          <>
            <div className="relative w-full h-full group">
              <img
                src={imageSource}
                alt={name}
                className="w-full h-full object-cover cursor-pointer transition-all duration-200 group-hover:brightness-90"
                onClick={handleImageClick}
                title="Haz clic para ampliar"
              />
              <div
                className="absolute inset-0 flex items-center justify-center bg-black/0 opacity-0 group-hover:opacity-100 group-hover:bg-black/20 transition-all duration-200 cursor-pointer"
                onClick={handleImageClick}
              >
                <span className="bg-black/60 text-white px-3 py-1 rounded-full text-sm font-medium">
                  Ver imagen
                </span>
              </div>
            </div>
            <ImageModal
              isOpen={isImageModalOpen}
              onClose={() => setIsImageModalOpen(false)}
              imageUrl={imageSource}
              altText={`Imagen de ${name}`}
            />
          </>
        ) : (
          <div className="text-center text-muted-foreground">
            Sin imagen de perfil
          </div>
        )}
      </div>
      <CardHeader>
        <div className="text-xl font-semibold">{name}</div>
        {role && (
          <Badge variant="outline" className="mb-2">
            {role}
          </Badge>
        )}
        <div className="text-muted-foreground">{occupation || "Sin ocupación"}</div>
        <div className="text-sm">{age ? `${age} años` : ""}</div>

        {alias && alias.length > 0 && (
          <div className="mt-2">
            <div className="text-sm text-muted-foreground mb-1">También conocido como:</div>
            <div className="flex flex-wrap gap-1">
              {alias.map((aliasName, index) => (
                <Badge key={index} variant="secondary" className="text-xs">
                  {aliasName}
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardHeader>
    </Card>
  );
};

export default CharacterProfile;
