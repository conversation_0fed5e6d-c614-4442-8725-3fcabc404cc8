import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Plus, Loader2, User, Zap } from 'lucide-react';
import { useToast } from '@/hooks/use-toast';
import { createCharacter } from '@/lib/firebase/firestore/characters';
import { useFirebase } from '@/contexts/FirebaseContext';
import { Character } from '@/types/firestore-models';

interface QuickAddCharacterProps {
  projectId: string;
  onCharacterCreated: (character: Character) => void;
  onOpenFullEditor?: (character: Character) => void;
}

const QuickAddCharacter = ({ projectId, onCharacterCreated, onOpenFullEditor }: QuickAddCharacterProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [formData, setFormData] = useState({
    name: '',
    role: '',
    quickNotes: ''
  });
  const { toast } = useToast();
  const { currentUser } = useFirebase();

  const roleOptions = [
    'Protagonista',
    'Antagonista',
    'Personaje secundario',
    'Mentor',
    'Aliado',
    'Obstáculo',
    'Confidente',
    'Catalizador',
    'Otro'
  ];

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const resetForm = () => {
    setFormData({
      name: '',
      role: '',
      quickNotes: ''
    });
  };

  const handleSaveAndClose = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Nombre requerido",
        description: "Por favor, añade un nombre para el personaje.",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser) {
      toast({
        title: "Error de autenticación",
        description: "Debes estar autenticado para crear personajes.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const characterData = {
        name: formData.name.trim(),
        role: formData.role || 'Personaje secundario',
        notas_adicionales: formData.quickNotes.trim(),
        // Campos por defecto para creación rápida
        alias: [],
        image_url: '',
        color_tag: '#6366F1',
        descripcion_fisica: '',
        personalidad: [],
        traits: [],
        motivacion: '',
        internal_conflict: '',
        external_conflict: '',
        bio: '',
        arc_summary: '',
        initial_state: '',
        final_state: '',
        fecha_nacimiento: null,
        // Legacy fields
        age: '',
        occupation: '',
        physicalDescription: '',
        personality: '',
        background: '',
        motivation: '',
        strength: 50,
        intelligence: 50,
        charisma: 50,
        resilience: 50
      };

      const newCharacter = await createCharacter(projectId, characterData);
      
      onCharacterCreated(newCharacter);
      resetForm();
      setIsOpen(false);

      toast({
        title: "Personaje creado",
        description: `${formData.name} ha sido añadido al proyecto. Puedes editarlo más tarde para añadir detalles.`,
      });
    } catch (error) {
      console.error("Error creating quick character:", error);
      toast({
        title: "Error al crear personaje",
        description: "No se pudo crear el personaje. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveAndEdit = async () => {
    if (!formData.name.trim()) {
      toast({
        title: "Nombre requerido",
        description: "Por favor, añade un nombre para el personaje.",
        variant: "destructive",
      });
      return;
    }

    if (!currentUser) {
      toast({
        title: "Error de autenticación",
        description: "Debes estar autenticado para crear personajes.",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      const characterData = {
        name: formData.name.trim(),
        role: formData.role || 'Personaje secundario',
        notas_adicionales: formData.quickNotes.trim(),
        // Campos por defecto
        alias: [],
        image_url: '',
        color_tag: '#6366F1',
        descripcion_fisica: '',
        personalidad: [],
        traits: [],
        motivacion: '',
        internal_conflict: '',
        external_conflict: '',
        bio: '',
        arc_summary: '',
        initial_state: '',
        final_state: '',
        fecha_nacimiento: null,
        // Legacy fields
        age: '',
        occupation: '',
        physicalDescription: '',
        personality: '',
        background: '',
        motivation: '',
        strength: 50,
        intelligence: 50,
        charisma: 50,
        resilience: 50
      };

      const newCharacter = await createCharacter(projectId, characterData);
      
      onCharacterCreated(newCharacter);
      resetForm();
      setIsOpen(false);

      // Abrir el editor completo
      onOpenFullEditor?.(newCharacter);

      toast({
        title: "Personaje creado",
        description: `${formData.name} ha sido creado. Ahora puedes añadir más detalles.`,
      });
    } catch (error) {
      console.error("Error creating quick character:", error);
      toast({
        title: "Error al crear personaje",
        description: "No se pudo crear el personaje. Por favor, intenta de nuevo.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="gap-2">
          <Zap className="h-4 w-4" />
          Añadir Rápido
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <User className="h-5 w-5 text-primary" />
            Crear Personaje Rápido
          </DialogTitle>
          <DialogDescription>
            Captura la idea básica de tu personaje. Podrás añadir más detalles después.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-4 py-4">
          <div className="space-y-2">
            <label htmlFor="quick-name" className="text-sm font-medium">
              Nombre *
            </label>
            <Input
              id="quick-name"
              placeholder="Ej: Elena Martínez"
              value={formData.name}
              onChange={(e) => handleInputChange('name', e.target.value)}
              onKeyDown={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSaveAndClose();
                }
              }}
            />
          </div>

          <div className="space-y-2">
            <label htmlFor="quick-role" className="text-sm font-medium">
              Rol en la historia
            </label>
            <Select value={formData.role} onValueChange={(value) => handleInputChange('role', value)}>
              <SelectTrigger>
                <SelectValue placeholder="Selecciona un rol" />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map((role) => (
                  <SelectItem key={role} value={role}>
                    {role}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label htmlFor="quick-notes" className="text-sm font-medium">
              Notas rápidas
            </label>
            <Textarea
              id="quick-notes"
              placeholder="Idea inicial, características clave, o cualquier detalle que quieras recordar..."
              value={formData.quickNotes}
              onChange={(e) => handleInputChange('quickNotes', e.target.value)}
              rows={3}
            />
          </div>
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button
            variant="outline"
            onClick={() => setIsOpen(false)}
            disabled={isLoading}
          >
            Cancelar
          </Button>
          <Button
            variant="secondary"
            onClick={handleSaveAndEdit}
            disabled={isLoading || !formData.name.trim()}
            className="gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <User className="h-4 w-4" />
            )}
            Guardar y Editar
          </Button>
          <Button
            onClick={handleSaveAndClose}
            disabled={isLoading || !formData.name.trim()}
            className="gap-2"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <Plus className="h-4 w-4" />
            )}
            Guardar
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default QuickAddCharacter;
