
import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Shuffle } from 'lucide-react';

// Sample data for the name generator and occupations
const firstNames = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
];

const lastNames = [
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
  "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>"
];

const occupations = [
  "Profesor/a", "Médico/a", "Artista", "Ingeniero/a", "Escritor/a",
  "Detective", "Científico/a", "Abogado/a", "Músico", "Chef",
  "Periodista", "Atleta", "Arquitecto/a", "Empresario/a", "Investigador/a"
];

interface CharacterGeneratorProps {
  generateRandomName: () => void;
  generateRandomOccupation: () => void;
}

const CharacterGenerator: React.FC<CharacterGeneratorProps> = ({ 
  generateRandomName,
  generateRandomOccupation 
}) => {
  return (
    <div className="flex gap-2 mb-4">
      <Button type="button" variant="outline" size="sm" onClick={generateRandomName}>
        <Shuffle className="mr-2 h-4 w-4" />
        Generar Nombre
      </Button>
      <Button type="button" variant="outline" size="sm" onClick={generateRandomOccupation}>
        <Shuffle className="mr-2 h-4 w-4" />
        Generar Ocupación
      </Button>
    </div>
  );
};

export default CharacterGenerator;
export { firstNames, lastNames, occupations };
