import { ReactNode } from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';
import { ChevronDown, Settings } from 'lucide-react';

interface AdvancedSectionProps {
  title: string;
  description?: string;
  children: ReactNode;
  defaultOpen?: boolean;
  icon?: ReactNode;
  className?: string;
}

export const AdvancedSection = ({ 
  title, 
  description, 
  children, 
  defaultOpen = false,
  icon = <Settings className="h-4 w-4" />,
  className = ""
}: AdvancedSectionProps) => {
  return (
    <Accordion type="single" collapsible defaultValue={defaultOpen ? "advanced" : undefined} className={className}>
      <AccordionItem value="advanced" className="border rounded-lg px-4">
        <AccordionTrigger className="hover:no-underline py-4">
          <div className="flex items-center gap-3 text-left">
            <div className="text-muted-foreground">
              {icon}
            </div>
            <div>
              <div className="font-medium text-sm">{title}</div>
              {description && (
                <div className="text-xs text-muted-foreground mt-1">{description}</div>
              )}
            </div>
          </div>
        </AccordionTrigger>
        <AccordionContent className="pb-4 pt-2">
          {children}
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};

interface AdvancedFieldGroupProps {
  title: string;
  description?: string;
  children: ReactNode;
  defaultOpen?: boolean;
  variant?: 'default' | 'compact';
}

export const AdvancedFieldGroup = ({ 
  title, 
  description, 
  children, 
  defaultOpen = false,
  variant = 'default'
}: AdvancedFieldGroupProps) => {
  if (variant === 'compact') {
    return (
      <div className="space-y-3">
        <div className="border-l-2 border-primary/20 pl-4">
          <h4 className="text-sm font-medium text-foreground">{title}</h4>
          {description && (
            <p className="text-xs text-muted-foreground mt-1">{description}</p>
          )}
        </div>
        <div className="space-y-4">
          {children}
        </div>
      </div>
    );
  }

  return (
    <Accordion type="single" collapsible defaultValue={defaultOpen ? "group" : undefined}>
      <AccordionItem value="group" className="border-0">
        <AccordionTrigger className="hover:no-underline py-3 px-0">
          <div className="text-left">
            <div className="font-medium text-sm">{title}</div>
            {description && (
              <div className="text-xs text-muted-foreground mt-1">{description}</div>
            )}
          </div>
        </AccordionTrigger>
        <AccordionContent className="pb-0 pt-2">
          <div className="space-y-4">
            {children}
          </div>
        </AccordionContent>
      </AccordionItem>
    </Accordion>
  );
};
