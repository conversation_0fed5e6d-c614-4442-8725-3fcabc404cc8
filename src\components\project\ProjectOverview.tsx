
import { Users, BookOpen, Layers, FileText, Sparkles, MapPin } from 'lucide-react';
import ModuleCard from './ModuleCard';

interface ProjectOverviewProps {
  characterCount: number;
  outlineCount: number;
  locationCount: number;
  onModuleClick: (moduleId: string) => void;
}

const ProjectOverview = ({ characterCount, outlineCount, locationCount, onModuleClick }: ProjectOverviewProps) => {
  const modules = [
    {
      id: 'characters',
      name: 'Personaj<PERSON>',
      icon: <Users className="h-5 w-5" />,
      count: characterCount
    },
    {
      id: 'locations',
      name: 'Localizaciones',
      icon: <MapPin className="h-5 w-5" />,
      count: locationCount
    },
    {
      id: 'documentation',
      name: 'Documentación',
      icon: <BookOpen className="h-5 w-5" />,
      count: 0
    },
    {
      id: 'outline',
      name: 'Escaletas',
      icon: <Layers className="h-5 w-5" />,
      count: outlineCount
    },
    {
      id: 'chapters',
      name: '<PERSON>í<PERSON><PERSON>',
      icon: <FileText className="h-5 w-5" />,
      count: 0
    },
    {
      id: 'assistant',
      name: '<PERSON><PERSON><PERSON>',
      icon: <Sparkles className="h-5 w-5" />,
      count: 0
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {modules.map((module) => (
        <ModuleCard
          key={module.id}
          id={module.id}
          name={module.name}
          icon={module.icon}
          count={module.count}
          onClick={onModuleClick}
        />
      ))}
    </div>
  );
};

export default ProjectOverview;
