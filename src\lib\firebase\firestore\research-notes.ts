import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  getDoc,
  query,
  where,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { ResearchNote, ResearchNoteWithoutId } from "@/types/firestore-models";

/**
 * Get the research_notes subcollection for a project
 */
export const getResearchNotesCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "research_notes");
};

/**
 * Create a new research note in a project
 */
export const createResearchNote = async (
  projectId: string, 
  noteData: ResearchNoteWithoutId
): Promise<ResearchNote> => {
  try {
    const notesCollection = getResearchNotesCollection(projectId);
    
    const docRef = await addDoc(notesCollection, {
      ...noteData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return { 
      id: docRef.id, 
      ...noteData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating research note:", error);
    throw error;
  }
};

/**
 * Get all research notes for a project
 */
export const getProjectResearchNotes = async (projectId: string): Promise<ResearchNote[]> => {
  try {
    const notesCollection = getResearchNotesCollection(projectId);
    const querySnapshot = await getDocs(notesCollection);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ResearchNote[];
  } catch (error) {
    console.error("Error fetching project research notes:", error);
    throw error;
  }
};

/**
 * Get research notes by tag
 */
export const getResearchNotesByTag = async (
  projectId: string, 
  tag: string
): Promise<ResearchNote[]> => {
  try {
    const notesCollection = getResearchNotesCollection(projectId);
    const q = query(notesCollection, where("tags", "array-contains", tag));
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as ResearchNote[];
  } catch (error) {
    console.error("Error fetching research notes by tag:", error);
    throw error;
  }
};

/**
 * Get a research note by ID
 */
export const getResearchNoteById = async (
  projectId: string, 
  noteId: string
): Promise<ResearchNote> => {
  try {
    const noteRef = doc(db, "projects", projectId, "research_notes", noteId);
    const noteSnap = await getDoc(noteRef);
    
    if (noteSnap.exists()) {
      return { 
        id: noteSnap.id, 
        ...noteSnap.data() 
      } as ResearchNote;
    } else {
      throw new Error("Research note not found");
    }
  } catch (error) {
    console.error("Error fetching research note:", error);
    throw error;
  }
};

/**
 * Update a research note
 */
export const updateResearchNote = async (
  projectId: string,
  noteId: string, 
  noteData: Partial<ResearchNote>
): Promise<Partial<ResearchNote>> => {
  try {
    const noteRef = doc(db, "projects", projectId, "research_notes", noteId);
    
    const updateData = {
      ...noteData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(noteRef, updateData);
    
    return { 
      id: noteId, 
      ...noteData 
    };
  } catch (error) {
    console.error("Error updating research note:", error);
    throw error;
  }
};

/**
 * Delete a research note
 */
export const deleteResearchNote = async (
  projectId: string, 
  noteId: string
): Promise<{ success: boolean }> => {
  try {
    const noteRef = doc(db, "projects", projectId, "research_notes", noteId);
    await deleteDoc(noteRef);
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting research note:", error);
    throw error;
  }
};

/**
 * Delete all research notes for a project (used when deleting a project)
 */
export const deleteAllProjectResearchNotes = async (projectId: string): Promise<void> => {
  try {
    const notesCollection = getResearchNotesCollection(projectId);
    const querySnapshot = await getDocs(notesCollection);
    
    const batch = writeBatch(db);
    
    querySnapshot.docs.forEach(noteDoc => {
      const noteRef = doc(db, "projects", projectId, "research_notes", noteDoc.id);
      batch.delete(noteRef);
    });
    
    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project research notes:", error);
    throw error;
  }
};

/**
 * Get a reference to a research note document
 */
export const getResearchNoteRef = (projectId: string, noteId: string): DocumentReference => {
  return doc(db, "projects", projectId, "research_notes", noteId);
};
