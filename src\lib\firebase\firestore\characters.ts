import {
  collection,
  addDoc,
  updateDoc,
  deleteDoc,
  doc,
  getDocs,
  getDoc,
  query,
  where,
  serverTimestamp,
  DocumentReference,
  writeBatch,
  collectionGroup
} from "firebase/firestore";
import { db } from "../config";
import { Character, CharacterWithoutId, CharacterRelationship, CharacterRelationshipWithoutId } from "@/types/firestore-models";
import { getUserProjects } from "./projects";

/**
 * Get the characters subcollection for a project
 */
export const getCharactersCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "characters");
};

/**
 * Get the character relationships subcollection for a character
 */
export const getCharacterRelationshipsCollection = (projectId: string, characterId: string) => {
  return collection(db, "projects", projectId, "characters", characterId, "related_characters");
};

/**
 * Create a new character in a project
 */
export const createCharacter = async (
  projectId: string,
  characterData: CharacterWithoutId
): Promise<Character> => {
  try {
    const charactersCollection = getCharactersCollection(projectId);

    const docRef = await addDoc(charactersCollection, {
      ...characterData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      id: docRef.id,
      ...characterData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating character:", error);
    throw error;
  }
};

/**
 * Get all characters for a project with fallback to legacy structure
 */
export const getProjectCharacters = async (projectId: string): Promise<Character[]> => {
  try {
    // First, verify that the project exists and the user has access to it
    const projectRef = doc(db, "projects", projectId);
    const projectSnap = await getDoc(projectRef);

    if (!projectSnap.exists()) {
      throw new Error("Project not found");
    }

    // Store the project data for permission checks
    const projectData = projectSnap.data();
    console.log("Project data for permission check:", {
      projectId,
      userId: projectData.userId,
      currentAuth: "Using Firebase Auth"
    });

    // Get characters from the subcollection structure
    const charactersCollection = getCharactersCollection(projectId);
    const querySnapshot = await getDocs(charactersCollection);

    const characters = querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Character[];

    console.log(`Retrieved ${characters.length} characters from project`);
    return characters;
  } catch (error) {
    console.error("Error fetching project characters:", error);
    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
};

/**
 * Get a character by ID
 */
export const getCharacterById = async (projectId: string, characterId: string): Promise<Character> => {
  try {
    // Get the character from the subcollection structure
    const characterRef = doc(db, "projects", projectId, "characters", characterId);
    const characterSnap = await getDoc(characterRef);

    if (characterSnap.exists()) {
      console.log("Retrieved character from project subcollection");
      return {
        id: characterSnap.id,
        ...characterSnap.data()
      } as Character;
    } else {
      throw new Error("Character not found");
    }
  } catch (error) {
    console.error("Error fetching character:", error);
    throw error;
  }
};

/**
 * Update a character
 */
export const updateCharacter = async (
  projectId: string,
  characterId: string,
  characterData: Partial<Character>
): Promise<Partial<Character>> => {
  try {
    const characterRef = doc(db, "projects", projectId, "characters", characterId);

    const updateData = {
      ...characterData,
      updatedAt: serverTimestamp()
    };

    await updateDoc(characterRef, updateData);

    return {
      id: characterId,
      ...characterData
    };
  } catch (error) {
    console.error("Error updating character:", error);
    throw error;
  }
};

/**
 * Delete a character and its relationships
 */
export const deleteCharacter = async (projectId: string, characterId: string): Promise<{ success: boolean }> => {
  try {
    // Delete all relationships first
    await deleteAllCharacterRelationships(projectId, characterId);

    // Delete the character document
    const characterRef = doc(db, "projects", projectId, "characters", characterId);
    await deleteDoc(characterRef);

    return { success: true };
  } catch (error) {
    console.error("Error deleting character:", error);
    throw error;
  }
};

/**
 * Delete all characters for a project (used when deleting a project)
 */
export const deleteAllProjectCharacters = async (projectId: string): Promise<void> => {
  try {
    const charactersCollection = getCharactersCollection(projectId);
    const querySnapshot = await getDocs(charactersCollection);

    const batch = writeBatch(db);

    querySnapshot.docs.forEach(characterDoc => {
      // Delete the character document
      const characterRef = doc(db, "projects", projectId, "characters", characterDoc.id);
      batch.delete(characterRef);

      // Note: This doesn't delete subcollections of characters
      // For a complete deletion, you would need to delete relationships for each character
    });

    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project characters:", error);
    throw error;
  }
};

/**
 * Get a reference to a character document
 */
export const getCharacterRef = (projectId: string, characterId: string): DocumentReference => {
  return doc(db, "projects", projectId, "characters", characterId);
};

// Character Relationships

/**
 * Create a new character relationship
 */
export const createCharacterRelationship = async (
  projectId: string,
  characterId: string,
  relationshipData: CharacterRelationshipWithoutId
): Promise<CharacterRelationship> => {
  try {
    const relationshipsCollection = getCharacterRelationshipsCollection(projectId, characterId);

    const docRef = await addDoc(relationshipsCollection, {
      ...relationshipData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });

    return {
      id: docRef.id,
      ...relationshipData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating character relationship:", error);
    throw error;
  }
};

/**
 * Get all relationships for a character
 */
export const getCharacterRelationships = async (
  projectId: string,
  characterId: string
): Promise<CharacterRelationship[]> => {
  try {
    const relationshipsCollection = getCharacterRelationshipsCollection(projectId, characterId);
    const querySnapshot = await getDocs(relationshipsCollection);

    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as CharacterRelationship[];
  } catch (error) {
    console.error("Error fetching character relationships:", error);
    throw error;
  }
};

/**
 * Delete all relationships for a character (used when deleting a character)
 */
export const deleteAllCharacterRelationships = async (
  projectId: string,
  characterId: string
): Promise<void> => {
  try {
    const relationshipsCollection = getCharacterRelationshipsCollection(projectId, characterId);
    const querySnapshot = await getDocs(relationshipsCollection);

    const batch = writeBatch(db);

    querySnapshot.docs.forEach(relationshipDoc => {
      const relationshipRef = doc(
        db,
        "projects",
        projectId,
        "characters",
        characterId,
        "related_characters",
        relationshipDoc.id
      );
      batch.delete(relationshipRef);
    });

    await batch.commit();
  } catch (error) {
    console.error("Error deleting all character relationships:", error);
    throw error;
  }
};


