import { Firestore, enableNetwork, disableNetwork } from 'firebase/firestore';
import { db } from './config';

/**
 * Clase para manejar la conexión a Firestore
 * Proporciona métodos para reconectar en caso de errores de red
 */
export class FirestoreConnectionManager {
  private static instance: FirestoreConnectionManager;
  private db: Firestore;
  private isReconnecting: boolean = false;
  private reconnectAttempts: number = 0;
  private maxReconnectAttempts: number = 5;
  private reconnectDelay: number = 2000; // 2 segundos iniciales

  private constructor(firestore: Firestore) {
    this.db = firestore;
  }

  /**
   * Obtiene la instancia singleton del administrador de conexiones
   */
  public static getInstance(): FirestoreConnectionManager {
    if (!FirestoreConnectionManager.instance) {
      FirestoreConnectionManager.instance = new FirestoreConnectionManager(db);
    }
    return FirestoreConnectionManager.instance;
  }

  /**
   * Intenta reconectar a Firestore después de un error de conexión
   * Utiliza un retroceso exponencial para los reintentos
   */
  public async reconnect(): Promise<boolean> {
    if (this.isReconnecting) {
      console.log('Ya hay un intento de reconexión en curso');
      return false;
    }

    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('Se alcanzó el número máximo de intentos de reconexión');
      this.resetReconnectState();
      return false;
    }

    this.isReconnecting = true;
    this.reconnectAttempts++;

    try {
      console.log(`Intento de reconexión ${this.reconnectAttempts}/${this.maxReconnectAttempts}`);
      
      // Desactivar la red para limpiar cualquier conexión pendiente
      await disableNetwork(this.db);
      
      // Esperar un tiempo antes de volver a activar
      await new Promise(resolve => setTimeout(resolve, this.calculateBackoff()));
      
      // Reactivar la red
      await enableNetwork(this.db);
      
      console.log('Reconexión exitosa a Firestore');
      this.resetReconnectState();
      return true;
    } catch (error) {
      console.error('Error durante la reconexión a Firestore:', error);
      this.isReconnecting = false;
      
      // Programar otro intento si no hemos alcanzado el máximo
      if (this.reconnectAttempts < this.maxReconnectAttempts) {
        setTimeout(() => this.reconnect(), this.calculateBackoff());
      } else {
        this.resetReconnectState();
      }
      
      return false;
    }
  }

  /**
   * Calcula el tiempo de espera para el próximo intento usando retroceso exponencial
   */
  private calculateBackoff(): number {
    // Retroceso exponencial: 2s, 4s, 8s, 16s, 32s
    return this.reconnectDelay * Math.pow(2, this.reconnectAttempts - 1);
  }

  /**
   * Reinicia el estado de reconexión
   */
  private resetReconnectState(): void {
    this.isReconnecting = false;
    this.reconnectAttempts = 0;
  }
}

/**
 * Función de utilidad para manejar errores de Firestore
 * Intenta reconectar automáticamente si es un error de red
 */
export const handleFirestoreError = async (error: any): Promise<boolean> => {
  console.error('Error en operación de Firestore:', error);
  
  // Verificar si es un error de red
  const isNetworkError = 
    error.code === 'failed-precondition' || 
    error.code === 'unavailable' || 
    error.code === 'deadline-exceeded' ||
    error.message?.includes('network') ||
    error.message?.includes('timeout') ||
    error.message?.includes('connection') ||
    error.message?.includes('QUIC_PROTOCOL_ERROR');
  
  if (isNetworkError) {
    console.log('Detectado error de red, intentando reconectar...');
    return await FirestoreConnectionManager.getInstance().reconnect();
  }
  
  return false;
};
