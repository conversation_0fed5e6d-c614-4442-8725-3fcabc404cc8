import React, { useState } from 'react';
import RichTextEditor from './rich-text-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';

/**
 * Componente de demostración para mostrar las nuevas funcionalidades del editor
 * Este componente puede ser usado para testing y documentación
 */
const RichTextEditorDemo: React.FC = () => {
  const [content, setContent] = useState(`
    <h1>¡Editor Completo - Fases 1 y 2!</h1>

    <p>Este editor ahora incluye <strong>funcionalidades avanzadas</strong> de escritura asistida:</p>

    <h2>Fase 1 - Formato Avanzado:</h2>
    <ul>
      <li><strong>Negrita</strong>, <em>cursiva</em>, <u>subrayado</u> y <s>tachado</s></li>
      <li>Texto con <span style="color: #EF4444">colores</span> y <mark style="background-color: #FEF3C7">resaltado</mark></li>
      <li>Subíndices como H₂O y superíndices como E=mc²</li>
      <li><a href="https://ejemplo.com">Enlaces funcionales</a></li>
    </ul>

    <h2>Fase 2 - Asistencia a la Escritura:</h2>
    <ul>
      <li>🔍 <strong>Buscar y reemplazar</strong> texto</li>
      <li>💾 <strong>Autoguardado</strong> con feedback visual</li>
      <li>⛶ <strong>Modo pantalla completa</strong> para concentración</li>
      <li>@ <strong>Menciones</strong> de personajes y localizaciones</li>
      <li>📊 <strong>Tablas</strong> para organizar información</li>
    </ul>

    <table>
      <tr>
        <th>Personaje</th>
        <th>Rol</th>
        <th>Estado</th>
      </tr>
      <tr>
        <td>Ana</td>
        <td>Protagonista</td>
        <td>Activo</td>
      </tr>
      <tr>
        <td>Carlos</td>
        <td>Antagonista</td>
        <td>En desarrollo</td>
      </tr>
    </table>

    <blockquote>
      "Escribe '@' para mencionar entidades del proyecto"
    </blockquote>

    <hr>

    <p>¡Prueba todas las herramientas nuevas!</p>
  `);

  // Datos de ejemplo para menciones
  const sampleCharacters = [
    { id: '1', name: 'Ana García' },
    { id: '2', name: 'Carlos Mendoza' },
    { id: '3', name: 'Elena Ruiz' },
  ];

  const sampleLocations = [
    { id: '1', name: 'Café Central' },
    { id: '2', name: 'Universidad' },
    { id: '3', name: 'Parque Municipal' },
  ];

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    console.log('Contenido actualizado:', newContent);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📝 Editor de Contenido - Fases 1 y 2 Completadas
          </CardTitle>
          <CardDescription>
            Formato avanzado + Asistencia a la escritura implementadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Funcionalidades Implementadas:</h3>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-blue-600">Fase 1 - Formato:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Subrayado, tachado, sub/superíndice</li>
                    <li>Colores de texto (12 opciones)</li>
                    <li>Resaltado (8 colores)</li>
                    <li>Alineación de texto</li>
                    <li>Enlaces y líneas horizontales</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-600">Fase 2 - Asistencia:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Autoguardado con feedback</li>
                    <li>Modo pantalla completa</li>
                    <li>Buscar y reemplazar</li>
                    <li>Menciones (@entidades)</li>
                    <li>Tablas básicas</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-purple-600">Próximamente:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Slash commands (/)</li>
                    <li>Toolbar personalizable</li>
                    <li>Indicadores de legibilidad</li>
                    <li>Plantillas de escena</li>
                    <li>Historial de versiones</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">Editor en Acción:</h3>
              <RichTextEditor
                value={content}
                onChange={handleContentChange}
                placeholder="Prueba todas las herramientas: formato, búsqueda, menciones (@), tablas y modo pantalla completa..."
                minHeight="400px"
                showWordCount={true}
                autoSave={true}
                autoSaveDelay={2000}
                characters={sampleCharacters}
                locations={sampleLocations}
                onAutoSave={(content) => console.log('Autoguardado:', content)}
                projectId="demo-project"
              />
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">Contenido HTML Generado:</h3>
              <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-32">
                {content}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RichTextEditorDemo;
