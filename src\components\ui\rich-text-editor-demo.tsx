import React, { useState } from 'react';
import RichTextEditor from './rich-text-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';

/**
 * Componente de demostración para mostrar las nuevas funcionalidades del editor
 * Este componente puede ser usado para testing y documentación
 */
const RichTextEditorDemo: React.FC = () => {
  const [content, setContent] = useState(`
    <h1>¡Editor Completo - Fases 1 y 2!</h1>

    <p>Este editor ahora incluye <strong>funcionalidades avanzadas</strong> de escritura asistida:</p>

    <h2>Fase 1 - Formato Avanzado:</h2>
    <ul>
      <li><strong>Negrita</strong>, <em>cursiva</em>, <u>subrayado</u> y <s>tachado</s></li>
      <li>Texto con <span style="color: #EF4444">colores</span> y <mark style="background-color: #FEF3C7">resaltado</mark></li>
      <li>Subíndices como H₂O y superíndices como E=mc²</li>
      <li><a href="https://ejemplo.com">Enlaces funcionales</a></li>
    </ul>

    <h2>Fase 2 - Asistencia a la Escritura:</h2>
    <ul>
      <li>🔍 <strong>Buscar y reemplazar</strong> texto</li>
      <li>💾 <strong>Autoguardado</strong> con feedback visual</li>
      <li>⛶ <strong>Modo pantalla completa</strong> para concentración</li>
      <li>@ <strong>Menciones</strong> de personajes y localizaciones</li>
      <li>📊 <strong>Tablas</strong> para organizar información</li>
    </ul>

    <table>
      <tr>
        <th>Personaje</th>
        <th>Rol</th>
        <th>Estado</th>
      </tr>
      <tr>
        <td>Ana</td>
        <td>Protagonista</td>
        <td>Activo</td>
      </tr>
      <tr>
        <td>Carlos</td>
        <td>Antagonista</td>
        <td>En desarrollo</td>
      </tr>
    </table>

    <blockquote>
      "Escribe '@' para mencionar entidades del proyecto"
    </blockquote>

    <hr>

    <p>¡Prueba todas las herramientas nuevas!</p>
  `);

  // Datos de ejemplo para menciones
  const sampleCharacters = [
    { id: '1', name: 'Ana García' },
    { id: '2', name: 'Carlos Mendoza' },
    { id: '3', name: 'Elena Ruiz' },
  ];

  const sampleLocations = [
    { id: '1', name: 'Café Central' },
    { id: '2', name: 'Universidad' },
    { id: '3', name: 'Parque Municipal' },
  ];

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    console.log('Contenido actualizado:', newContent);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📝 Editor de Contenido - ¡TODAS LAS FASES COMPLETADAS!
          </CardTitle>
          <CardDescription>
            Formato avanzado + Asistencia a la escritura + UX y Productividad avanzada
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">🎉 ¡TODAS LAS FUNCIONALIDADES IMPLEMENTADAS!</h3>
              <div className="grid grid-cols-3 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-blue-600">✅ Fase 1 - Formato:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Subrayado, tachado, sub/superíndice</li>
                    <li>Colores de texto (12 opciones)</li>
                    <li>Resaltado (8 colores)</li>
                    <li>Alineación de texto</li>
                    <li>Enlaces y líneas horizontales</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-600">✅ Fase 2 - Asistencia:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Autoguardado con feedback</li>
                    <li>Modo pantalla completa</li>
                    <li>Buscar y reemplazar</li>
                    <li>Menciones (@entidades)</li>
                    <li>Tablas básicas</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-purple-600">✅ Fase 3 - UX Avanzada:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>⚡ Slash commands (/)</li>
                    <li>📊 Indicadores de legibilidad</li>
                    <li>📋 Plantillas de escena</li>
                    <li>📚 Historial de versiones</li>
                    <li>⚙️ Configuración de toolbar</li>
                  </ul>
                </div>
              </div>

              <div className="mt-4 p-4 bg-green-50 border border-green-200 rounded-lg">
                <h4 className="font-medium text-green-800 mb-2">🚀 Editor Profesional Completo</h4>
                <p className="text-sm text-green-700">
                  El editor ahora rivaliza con herramientas como Notion y Google Docs, pero está específicamente
                  diseñado para escritores creativos. ¡Prueba escribir "/" para ver los comandos rápidos!
                </p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">Editor en Acción:</h3>
              <RichTextEditor
                value={content}
                onChange={handleContentChange}
                placeholder="¡Editor completo! Prueba: '/' para comandos, '@' para menciones, todas las herramientas de formato, plantillas, versiones y estadísticas de legibilidad..."
                minHeight="400px"
                showWordCount={true}
                showReadabilityStats={true}
                autoSave={true}
                autoSaveDelay={2000}
                characters={sampleCharacters}
                locations={sampleLocations}
                onAutoSave={(content) => console.log('Autoguardado:', content)}
                projectId="demo-project"
                enableSlashCommands={true}
                enableVersionHistory={true}
                templates={[
                  {
                    id: 'action',
                    name: 'Escena de Acción',
                    content: '<h2>🎬 Escena de Acción</h2><p><strong>Ubicación:</strong> [Describe el lugar]</p><p><strong>Personajes:</strong> [Lista los personajes]</p><p><strong>Objetivo:</strong> [¿Qué debe lograr esta escena?]</p><hr><p>La tensión se palpaba en el aire cuando...</p><p><em>Continúa escribiendo tu escena de acción aquí...</em></p>'
                  },
                  {
                    id: 'dialogue',
                    name: 'Escena de Diálogo',
                    content: '<h2>💬 Escena de Diálogo</h2><p><strong>Ubicación:</strong> [Describe el lugar]</p><p><strong>Personajes:</strong> [Lista los personajes]</p><p><strong>Conflicto:</strong> [¿Cuál es la tensión?]</p><hr><blockquote>"Necesitamos hablar," dijo [Personaje] con voz tensa.</blockquote><p><em>Desarrolla el diálogo aquí...</em></p>'
                  },
                  {
                    id: 'description',
                    name: 'Escena Descriptiva',
                    content: '<h2>🌅 Escena Descriptiva</h2><p><strong>Ubicación:</strong> [Describe el lugar]</p><p><strong>Atmósfera:</strong> [¿Qué sensación transmite?]</p><p><strong>Elementos clave:</strong> [Objetos, sonidos, olores importantes]</p><hr><p>El lugar se extendía ante ellos como un lienzo en blanco esperando ser pintado...</p><p><em>Continúa con la descripción detallada...</em></p>'
                  },
                  {
                    id: 'mystery',
                    name: 'Escena de Misterio',
                    content: '<h2>🔍 Escena de Misterio</h2><p><strong>Ubicación:</strong> [Lugar del misterio]</p><p><strong>Pistas:</strong> [¿Qué se descubre?]</p><p><strong>Tensión:</strong> [¿Qué genera suspense?]</p><hr><p>Algo no encajaba. Los detalles que había pasado por alto ahora cobraban un significado siniestro...</p>'
                  }
                ]}
                onSaveVersion={(content, title) => {
                  console.log(`📚 Guardando versión "${title}":`, content.substring(0, 100) + '...');
                  alert(`✅ Versión "${title}" guardada exitosamente!`);
                }}
              />
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">Contenido HTML Generado:</h3>
              <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-32">
                {content}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RichTextEditorDemo;
