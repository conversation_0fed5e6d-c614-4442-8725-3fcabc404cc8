import React, { useState } from 'react';
import RichTextEditor from './rich-text-editor';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from './card';

/**
 * Componente de demostración para mostrar las nuevas funcionalidades del editor
 * Este componente puede ser usado para testing y documentación
 */
const RichTextEditorDemo: React.FC = () => {
  const [content, setContent] = useState(`
    <h1>¡Bienvenido al Editor Mejorado!</h1>
    
    <p>Este editor ahora incluye <strong>formato avanzado</strong> con múltiples opciones:</p>
    
    <ul>
      <li><strong>Negrita</strong>, <em>cursiva</em>, <u>subrayado</u> y <s>tachado</s></li>
      <li>Texto con <span style="color: #EF4444">colores</span> y <mark style="background-color: #FEF3C7">resaltado</mark></li>
      <li>Subíndices como H₂O y superíndices como E=mc²</li>
      <li><a href="https://ejemplo.com">Enlaces funcionales</a></li>
    </ul>
    
    <div style="text-align: center">
      <p>Texto centrado con alineación personalizada</p>
    </div>
    
    <blockquote>
      "Las citas se ven perfectas para diálogos importantes"
    </blockquote>
    
    <hr>
    
    <p>¡Prueba todas las herramientas de la barra superior!</p>
  `);

  const handleContentChange = (newContent: string) => {
    setContent(newContent);
    console.log('Contenido actualizado:', newContent);
  };

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            📝 Editor de Contenido - Fase 1 Completada
          </CardTitle>
          <CardDescription>
            Nuevas funcionalidades de formato y estilo avanzado implementadas
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-semibold mb-2">Funcionalidades Nuevas:</h3>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-medium text-blue-600">Formato de Texto:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Subrayado (Ctrl+U)</li>
                    <li>Tachado</li>
                    <li>Subíndice y Superíndice</li>
                    <li>Colores de texto (12 opciones)</li>
                    <li>Resaltado (8 colores)</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-medium text-green-600">Herramientas:</h4>
                  <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                    <li>Alineación (izq, centro, der, justificar)</li>
                    <li>Enlaces con diálogo</li>
                    <li>Líneas horizontales</li>
                    <li>Limpiar formato</li>
                  </ul>
                </div>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">Editor en Acción:</h3>
              <RichTextEditor
                value={content}
                onChange={handleContentChange}
                placeholder="Prueba las nuevas herramientas de formato..."
                minHeight="400px"
                showWordCount={true}
              />
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-semibold mb-2">Contenido HTML Generado:</h3>
              <pre className="bg-muted p-3 rounded text-xs overflow-auto max-h-32">
                {content}
              </pre>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RichTextEditorDemo;
