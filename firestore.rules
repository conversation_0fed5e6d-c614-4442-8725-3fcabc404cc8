// Firestore Security Rules
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {

    // Helper function to check if the user is authenticated
    function isAuthenticated() {
      return request.auth != null;
    }

    // Helper function to check if the user is the owner of a document
    // Ensures the user is authenticated and their UID matches the document's userId field
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }

    // Helper function to check if the user creating a document is assigning it to themselves
    function isCreatingForSelf() {
      return isAuthenticated() && request.auth.uid == request.resource.data.userId;
    }

    // Helper function to validate basic project data structure
    function isValidProjectData() {
      return request.resource.data.title is string &&
             request.resource.data.title.size() > 0 &&
             request.resource.data.userId is string &&
             request.resource.data.userId.size() > 0;
    }

    // --- Projects Collection and Subcollections ---
    match /projects/{projectId} {
      // Read: Allow if the user is the owner of the project
      allow read: if isOwner(resource.data.userId);

      // Create: Allow if the authenticated user is setting themselves as the owner
      // This prevents a user from creating a project and assigning it to someone else.
      // Also validates basic data structure for enhanced security
      allow create: if isCreatingForSelf() && isValidProjectData();

      // Update, Delete: Allow if the user is the owner of the project
      allow update, delete: if isOwner(resource.data.userId);

      // --- Subcollections (characters, locations, outlines, chapters, scenes, etc.) ---
      // Read, Write (Create, Update, Delete):
      // Allow if the user is the owner of the parent project.
      // This rule uses get() which counts as one read operation per access to a subcollection document.
      match /{subcollection}/{documentId} {
        allow read, write: if isAuthenticated() &&
                              get(/databases/$(database)/documents/projects/$(projectId)).data.userId == request.auth.uid;
      }

      // --- Nested Subcollections (e.g., character_relationships) ---
      // Read, Write (Create, Update, Delete):
      // Allow if the user is the owner of the grandparent project.
      match /{subcollection}/{documentId}/{nestedSubcollection}/{nestedDocumentId} {
        allow read, write: if isAuthenticated() &&
                              get(/databases/$(database)/documents/projects/$(projectId)).data.userId == request.auth.uid;
      }
    }

    // --- Legacy collections have been removed ---
    // All data is now managed within project subcollections for better security and organization.

    // --- Data Validation (Future Enhancement) ---
    // For enhanced data integrity, consider adding data validation rules.
    // This ensures that the data being written matches the expected schema.
    // Example (conceptual for project creation):
    // allow create: if isCreatingForSelf() &&
    //                  request.resource.data.title is string &&
    //                  request.resource.data.title.size() > 0 &&
    //                  request.resource.data.genre is string;
    //
    // Data validation can become verbose but adds an important layer of protection.
  }
}
