
import { useState, useEffect, useCallback } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Play, 
  Pause, 
  RefreshCw, 
  Clock
} from 'lucide-react';

interface TimerProps {
  initialMinutes?: number;
  onTimerEnd?: () => void;
}

const Timer = ({ initialMinutes = 5, onTimerEnd }: TimerProps) => {
  const [seconds, setSeconds] = useState(initialMinutes * 60);
  const [isActive, setIsActive] = useState(false);
  const [isPaused, setIsPaused] = useState(false);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  const resetTimer = useCallback(() => {
    setSeconds(initialMinutes * 60);
    setIsActive(false);
    setIsPaused(false);
  }, [initialMinutes]);

  const toggleTimer = () => {
    if (!isActive) {
      setIsActive(true);
      setIsPaused(false);
    } else {
      setIsPaused(!isPaused);
    }
  };

  useEffect(() => {
    let interval: NodeJS.Timeout | null = null;

    if (isActive && !isPaused) {
      interval = setInterval(() => {
        setSeconds(seconds => {
          if (seconds <= 1) {
            if (interval) clearInterval(interval);
            if (onTimerEnd) onTimerEnd();
            return 0;
          }
          return seconds - 1;
        });
      }, 1000);
    } else if (interval) {
      clearInterval(interval);
    }

    return () => {
      if (interval) clearInterval(interval);
    };
  }, [isActive, isPaused, onTimerEnd]);

  const progressPercent = ((initialMinutes * 60 - seconds) / (initialMinutes * 60)) * 100;

  return (
    <div className="w-full max-w-sm mx-auto">
      <div className="relative flex flex-col items-center justify-center p-6 bg-card rounded-lg shadow-sm border">
        <Clock className="w-8 h-8 text-primary mb-2" />
        <h3 className="font-heading text-2xl font-semibold mb-4">
          {formatTime(seconds)}
        </h3>

        {/* Progress bar */}
        <div className="w-full h-2 bg-secondary rounded-full mb-4">
          <div 
            className="h-full bg-primary rounded-full transition-all duration-300 ease-linear"
            style={{ width: `${progressPercent}%` }}
          ></div>
        </div>

        <div className="flex space-x-2">
          <Button 
            variant="outline" 
            size="icon" 
            onClick={toggleTimer}
          >
            {isActive && !isPaused ? (
              <Pause className="h-4 w-4" />
            ) : (
              <Play className="h-4 w-4" />
            )}
          </Button>
          <Button 
            variant="outline" 
            size="icon"
            onClick={resetTimer}
          >
            <RefreshCw className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default Timer;
