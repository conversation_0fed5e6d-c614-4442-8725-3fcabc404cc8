
import React from 'react';
import { format } from "date-fns";
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Calendar } from '@/components/ui/calendar';
import { CalendarIcon, MapPin, Clock, BookText, Sparkles, Heart, Lightbulb, BookOpen, Target, Palette, FileText } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { Character } from '@/types/firestore-models';
import { Location } from '@/types/firestore-models';
import { CharacterSelector } from '../character/CharacterSelector';
import { DatePicker } from '../ui/date-picker';
import { TimePicker } from '../ui/time-picker';
import { AdvancedSection, AdvancedFieldGroup } from '@/components/ui/advanced-section';
import RichTextEditor from '@/components/ui/rich-text-editor';


interface SceneFormProps {
  title: string;
  description: string;
  location: string;
  selectedCharacters: string[];
  characters: Character[];
  locations?: Location[];
  sceneDate?: Date;
  sceneTime?: string;
  objetivoEscena?: string;
  tono?: string;
  elementoSimbolico?: string;
  desarrolloEmocional?: string;
  funcionHistoria?: string;
  puntoDeVista?: string;
  content?: string; // Contenido HTML del editor de texto enriquecido
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  onCharacterSelect: (characterId: string) => void;
  onLocationSelect?: (locationId: string) => void;
  onDateSelect?: (date: Date | undefined, time: string | undefined) => void;
  onPuntoDeVistaSelect?: (characterId: string) => void;
  onContentChange?: (content: string) => void; // Función para manejar cambios en el editor
}

const SceneForm: React.FC<SceneFormProps> = ({
  title,
  description,
  location,
  selectedCharacters,
  characters,
  locations = [],
  sceneDate,
  sceneTime,
  objetivoEscena = '',
  tono = '',
  elementoSimbolico = '',
  desarrolloEmocional = '',
  funcionHistoria = '',
  puntoDeVista = '',
  content = '',
  onInputChange,
  onCharacterSelect,
  onLocationSelect,
  onDateSelect,
  onPuntoDeVistaSelect,
  onContentChange
}) => {
  return (
    <Tabs defaultValue="basic" className="w-full">
      <TabsList className="grid w-full grid-cols-5 mb-6">
        <TabsTrigger value="basic" className="text-base py-3">
          <BookText className="h-5 w-5 mr-2 text-blue-500" />
          Información Básica
        </TabsTrigger>
        <TabsTrigger value="timing" className="text-base py-3">
          <Clock className="h-5 w-5 mr-2 text-amber-500" />
          Tiempo y Lugar
        </TabsTrigger>
        <TabsTrigger value="narrative" className="text-base py-3">
          <Sparkles className="h-5 w-5 mr-2 text-purple-500" />
          Elementos Narrativos
        </TabsTrigger>
        <TabsTrigger value="characters" className="text-base py-3">
          <Heart className="h-5 w-5 mr-2 text-pink-500" />
          Personajes
        </TabsTrigger>
        <TabsTrigger value="content" className="text-base py-3">
          <FileText className="h-5 w-5 mr-2 text-green-500" />
          Contenido
        </TabsTrigger>
      </TabsList>
      
      {/* Pestaña de Información Básica */}
      <TabsContent value="basic" className="space-y-6 py-4">
        <div>
          <Label htmlFor="sceneTitle" className="text-lg mb-2 block">Título de la escena</Label>
          <Input
            id="sceneTitle"
            name="title"
            value={title}
            onChange={onInputChange}
            placeholder="Título de la escena"
            className="text-lg py-6"
          />
        </div>
        
        <div>
          <Label htmlFor="sceneDescription" className="text-lg mb-2 block">Descripción</Label>
          <Textarea
            id="sceneDescription"
            name="description"
            value={description}
            onChange={onInputChange}
            placeholder="Descripción de la escena"
            rows={6}
            className="text-base min-h-[150px] resize-y"
          />
        </div>
      </TabsContent>
      
      {/* Pestaña de Tiempo y Lugar */}
      <TabsContent value="timing" className="space-y-6 py-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <Label className="flex items-center gap-2 text-lg mb-2 block">
              <CalendarIcon className="h-5 w-5 text-blue-500" />
              Fecha de la escena
            </Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal py-6 text-lg",
                    !sceneDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-5 w-5" />
                  {sceneDate && sceneDate instanceof Date && !isNaN(sceneDate.getTime()) ?
                    format(sceneDate, "PPP") :
                    <span>Seleccionar fecha</span>
                  }
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={sceneDate && sceneDate instanceof Date && !isNaN(sceneDate.getTime()) ? sceneDate : undefined}
                  onSelect={(date) => onDateSelect && onDateSelect(date, sceneTime)}
                  initialFocus
                  className={cn("p-3 pointer-events-auto")}
                />
              </PopoverContent>
            </Popover>
          </div>

          <div>
            <Label htmlFor="sceneTime" className="flex items-center gap-2 text-lg mb-2 block">
              <Clock className="h-5 w-5 text-amber-500" />
              Hora de la escena
            </Label>
            <Input
              id="sceneTime"
              name="sceneTime"
              type="time"
              value={sceneTime || ''}
              onChange={(e) => {
                onInputChange(e);
                if (onDateSelect) {
                  onDateSelect(sceneDate, e.target.value);
                }
              }}
              className="w-full py-6 text-lg"
            />
          </div>
        </div>
        
        <div className="w-full">
          <Label htmlFor="location" className="flex items-center gap-2 text-lg mb-2 block">
            <MapPin className="h-5 w-5 text-green-500" />
            Ubicación
          </Label>
          {locations && locations.length > 0 ? (
            <Select 
              value={location} 
              onValueChange={(value) => onLocationSelect && onLocationSelect(value)}
            >
              <SelectTrigger className="py-6 text-lg">
                <SelectValue placeholder="Selecciona una ubicación" />
              </SelectTrigger>
              <SelectContent>
                {locations.map((loc) => (
                  <SelectItem key={loc.id} value={loc.id}>
                    {loc.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <Input
              type="text"
              id="location"
              name="location"
              value={location}
              onChange={onInputChange}
              placeholder="Ubicación de la escena"
              className="py-6 text-lg"
            />
          )}
        </div>
      </TabsContent>
      
      {/* Pestaña de Elementos Narrativos */}
      <TabsContent value="narrative" className="space-y-6 py-4">
        {/* Información Esencial - Siempre visible */}
        <div>
          <Label htmlFor="puntoDeVista" className="flex items-center gap-2 text-lg mb-2 block">
            <BookOpen className="h-5 w-5 text-indigo-500" />
            Punto de Vista
          </Label>
          {characters.length > 0 ? (
            <Select
              value={puntoDeVista}
              onValueChange={(value) => onPuntoDeVistaSelect && onPuntoDeVistaSelect(value)}
            >
              <SelectTrigger className="py-6 text-lg">
                <SelectValue placeholder="Selecciona un personaje narrador" />
              </SelectTrigger>
              <SelectContent>
                {characters.map((character) => (
                  <SelectItem key={character.id} value={character.id}>
                    {character.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ) : (
            <div className="flex flex-col items-center justify-center h-[100px] border rounded-lg bg-muted/20">
              <p className="text-muted-foreground text-sm">No hay personajes disponibles para seleccionar como narrador.</p>
            </div>
          )}
        </div>

        <div>
          <Label htmlFor="objetivoEscena" className="flex items-center gap-2 text-lg mb-2 block">
            <Target className="h-5 w-5 text-blue-500" />
            Objetivo de la escena
          </Label>
          <Textarea
            id="objetivoEscena"
            name="objetivoEscena"
            value={objetivoEscena}
            onChange={onInputChange}
            placeholder="Explica qué función narrativa cumple la escena dentro de la historia"
            rows={3}
            className="text-base min-h-[100px] resize-y"
          />
        </div>

        {/* Elementos Narrativos Avanzados - Colapsable */}
        <AdvancedSection
          title="Elementos Narrativos Avanzados"
          description="Tono, simbolismo, desarrollo emocional y función narrativa"
          icon={<Palette className="h-4 w-4" />}
          defaultOpen={!!(tono || elementoSimbolico || desarrolloEmocional || funcionHistoria)}
        >
          <div className="space-y-4">
            <div>
              <Label htmlFor="tono" className="flex items-center gap-2 text-lg mb-2 block">
                <Sparkles className="h-5 w-5 text-amber-500" />
                Tono
              </Label>
              <Textarea
                id="tono"
                name="tono"
                value={tono}
                onChange={onInputChange}
                placeholder="Describe la atmósfera emocional o estilística predominante"
                rows={3}
                className="text-base min-h-[100px] resize-y"
              />
            </div>

            <div>
              <Label htmlFor="elementoSimbolico" className="flex items-center gap-2 text-lg mb-2 block">
                <Lightbulb className="h-5 w-5 text-yellow-500" />
                Elemento simbólico central
              </Label>
              <Textarea
                id="elementoSimbolico"
                name="elementoSimbolico"
                value={elementoSimbolico}
                onChange={onInputChange}
                placeholder="Identifica el objeto, imagen o acción que representa un significado más profundo o temático"
                rows={3}
                className="text-base min-h-[100px] resize-y"
              />
            </div>

            <div>
              <Label htmlFor="desarrolloEmocional" className="flex items-center gap-2 text-lg mb-2 block">
                <Heart className="h-5 w-5 text-pink-500" />
                Desarrollo emocional
              </Label>
              <Textarea
                id="desarrolloEmocional"
                name="desarrolloEmocional"
                value={desarrolloEmocional}
                onChange={onInputChange}
                placeholder="Resume la evolución del estado emocional del personaje principal durante la escena"
                rows={3}
                className="text-base min-h-[100px] resize-y"
              />
            </div>

            <div>
              <Label htmlFor="funcionHistoria" className="flex items-center gap-2 text-lg mb-2 block">
                <BookOpen className="h-5 w-5 text-green-500" />
                Función en la historia
              </Label>
              <Textarea
                id="funcionHistoria"
                name="funcionHistoria"
                value={funcionHistoria}
                onChange={onInputChange}
                placeholder="Indica cómo esta escena contribuye al arco general del relato o al desarrollo del protagonista"
                rows={3}
                className="text-base min-h-[100px] resize-y"
              />
            </div>
          </div>
        </AdvancedSection>
      </TabsContent>
      
      {/* Pestaña de Personajes */}
      <TabsContent value="characters" className="space-y-6 py-4">
        {characters.length > 0 ? (
          <div>
            <Label className="block mb-4 text-lg">Personajes en la escena</Label>
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
              {characters.map(character => {
                const isSelected = selectedCharacters.includes(character.id);
                return (
                  <Button
                    key={character.id}
                    type="button"
                    variant={isSelected ? "default" : "outline"}
                    className={cn(
                      "justify-start py-6 text-base",
                      isSelected ? "bg-primary text-primary-foreground" : "hover:bg-primary/10"
                    )}
                    onClick={() => onCharacterSelect(character.id)}
                  >
                    <span className="truncate">{character.name}</span>
                  </Button>
                );
              })}
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-[300px] border rounded-lg bg-muted/20">
            <p className="text-muted-foreground text-lg">No hay personajes disponibles para seleccionar.</p>
            <p className="text-muted-foreground text-sm mt-2">Crea personajes en la sección de personajes para poder añadirlos a tus escenas.</p>
          </div>
        )}
      </TabsContent>

      {/* Pestaña de Contenido */}
      <TabsContent value="content" className="space-y-6 py-4">
        <div>
          <Label className="block mb-4 text-lg flex items-center gap-2">
            <FileText className="h-5 w-5 text-green-500" />
            Contenido de la escena
          </Label>
          <p className="text-sm text-muted-foreground mb-4">
            Escribe el contenido narrativo de tu escena. Puedes incluir diálogos, descripciones, acciones y cualquier detalle que forme parte de la narrativa.
          </p>
          <RichTextEditor
            value={content}
            onChange={onContentChange || (() => {})}
            placeholder="Comienza a escribir tu escena aquí... Puedes usar '@' para mencionar personajes o localizaciones, y las herramientas de formato para dar estilo a tu texto."
            minHeight="500px"
            showWordCount={true}
            autoSave={true}
            autoSaveDelay={3000}
            characters={characters}
            locations={locations}
            onAutoSave={onContentChange}
          />
        </div>
      </TabsContent>
    </Tabs>
  );
};

export default SceneForm;
