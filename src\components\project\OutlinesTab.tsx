import React, { useState, Suspense } from 'react';
import { Link } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useToast } from '@/hooks/use-toast';
import { Outline } from '@/types/outline';
import { LazyOutlineCreator } from '../../pages/lazy-pages';
import { ComponentLoadingFallback } from '@/components/ui/loading-fallback';
import QuickAddOutline from '../outline/QuickAddOutline';
import { Pen, Trash, Eye, Plus } from 'lucide-react';

interface OutlinesTabProps {
  outlines: Outline[];
  projectId: string;
  onOutlineUpdated: (outline: Outline) => void;
  onDeleteOutline: (outlineId: string) => void;
  isDeleting: boolean;
}

const OutlinesTab: React.FC<OutlinesTabProps> = ({
  outlines,
  projectId,
  onOutlineUpdated,
  onDeleteOutline,
  isDeleting
}) => {
  const { toast } = useToast();
  const [isCreating, setIsCreating] = useState(false);
  const [editingOutline, setEditingOutline] = useState<Outline | null>(null);

  const handleCreateNew = () => {
    setIsCreating(true);
    setEditingOutline(null);
  };

  const handleEdit = (outline: Outline) => {
    setEditingOutline(outline);
    setIsCreating(false);
  };

  const handleCancel = () => {
    setIsCreating(false);
    setEditingOutline(null);
  };

  const handleOutlineUpdated = (outline: Outline) => {
    onOutlineUpdated(outline);
    setIsCreating(false);
    setEditingOutline(null);
    toast({
      title: "Escaleta guardada",
      description: "Los cambios han sido guardados con éxito.",
    });
  };

  const handleDelete = async (outlineId: string) => {
    if (confirm('¿Estás seguro de que deseas eliminar esta escaleta? Esta acción no se puede deshacer.')) {
      onDeleteOutline(outlineId);
    }
  };

  // Si estamos en modo de creación o edición, mostrar el componente OutlineCreator
  if (isCreating || editingOutline) {
    return (
      <Suspense fallback={<ComponentLoadingFallback message="Cargando editor de escaleta..." />}>
        <LazyOutlineCreator
          projectId={projectId}
          existingOutline={editingOutline || undefined}
          onOutlineUpdated={handleOutlineUpdated}
          onCancel={handleCancel}
        />
      </Suspense>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Escaletas</h2>
        <div className="flex gap-2">
          <QuickAddOutline
            projectId={projectId}
            onOutlineCreated={onOutlineUpdated}
            onOpenFullEditor={(outline) => {
              setEditingOutline(outline);
            }}
          />
          <Button onClick={handleCreateNew}>
            <Plus className="mr-2 h-4 w-4" />
            Nueva Escaleta
          </Button>
        </div>
      </div>

      {outlines.length === 0 ? (
        <div className="text-center p-8 border rounded-lg bg-muted">
          <p className="text-muted-foreground mb-4">
            No hay escaletas creadas para este proyecto.
          </p>
          <div className="flex gap-2 justify-center">
            <QuickAddOutline
              projectId={projectId}
              onOutlineCreated={onOutlineUpdated}
              onOpenFullEditor={(outline) => {
                setEditingOutline(outline);
              }}
            />
            <Button onClick={handleCreateNew}>
              <Plus className="mr-2 h-4 w-4" />
              Crear Escaleta
            </Button>
          </div>
        </div>
      ) : (
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          {outlines.map((outline) => (
            <Card key={outline.id}>
              <CardHeader>
                <CardTitle>{outline.title}</CardTitle>
                <CardDescription>
                  {outline.scenes.length} escenas
                </CardDescription>
              </CardHeader>
              <CardContent>
                <p className="line-clamp-2 text-muted-foreground">
                  {outline.description || "Sin descripción"}
                </p>
              </CardContent>
              <CardFooter className="flex justify-between">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleEdit(outline)}
                >
                  <Pen className="mr-2 h-4 w-4" />
                  Editar
                </Button>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    asChild
                  >
                    <Link to={`/projects/${projectId}/outlines/${outline.id}`}>
                      <Eye className="mr-2 h-4 w-4" />
                      Ver
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDelete(outline.id)}
                    disabled={isDeleting}
                  >
                    <Trash className="mr-2 h-4 w-4" />
                    Eliminar
                  </Button>
                </div>
              </CardFooter>
            </Card>
          ))}
        </div>
      )}
    </div>
  );
};

export default OutlinesTab;
