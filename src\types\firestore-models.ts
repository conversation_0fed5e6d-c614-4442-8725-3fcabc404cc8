import { Timestamp, DocumentReference } from "firebase/firestore";

// Project (Main Collection)
export interface Project {
  id: string;
  title: string;
  logline?: string;
  theme?: string;
  genre?: string;
  target_audience?: string;
  overall_status?: string;
  word_count_total?: number;
  target_word_count?: number;
  start_scene_id?: DocumentReference;
  coverImage?: string;
  userId: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type ProjectWithoutId = Omit<Project, 'id' | 'createdAt' | 'updatedAt'>;

// Character (Subcollection of Project)
export interface Character {
  id: string;
  name: string;
  alias?: string[];
  image_url?: string;
  color_tag?: string;
  role?: string;
  descripcion_fisica?: string;
  personalidad?: string[];
  traits?: string[];
  motivacion?: string;
  internal_conflict?: string;
  external_conflict?: string;
  bio?: string;
  arc_summary?: string;
  initial_state?: string;
  final_state?: string;
  fecha_nacimiento?: Timestamp | string | Date;
  notas_adicionales?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;

  // Legacy fields for backward compatibility
  age?: string;
  occupation?: string;
  physicalDescription?: string;
  personality?: string;
  background?: string;
  motivation?: string;
  strength?: number;
  intelligence?: number;
  charisma?: number;
  resilience?: number;
  profilePicture?: string;

  // Helper field for components that need project context
  projectId?: string;
  projectTitle?: string;

  [key: string]: any;
}

export type CharacterWithoutId = Omit<Character, 'id' | 'createdAt' | 'updatedAt'>;

// Character Relationship (Subcollection of Character)
export interface CharacterRelationship {
  id: string;
  target_character_ref: DocumentReference;
  relationship_type: string;
  description: string;
  status?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type CharacterRelationshipWithoutId = Omit<CharacterRelationship, 'id' | 'createdAt' | 'updatedAt'>;

// Chapter (Subcollection of Project)
export interface Chapter {
  id: string;
  title: string;
  order: number;
  summary?: string;
  status?: string;
  pov_characters?: DocumentReference[];
  word_count?: number;
  scene_order?: DocumentReference[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type ChapterWithoutId = Omit<Chapter, 'id' | 'createdAt' | 'updatedAt'>;

// Scene (Subcollection of Project)
export interface Scene {
  id: string;
  title?: string;
  global_order?: number;
  chapter_id: DocumentReference;
  content?: string;
  summary?: string;
  fecha_escena?: string;
  hora_escena?: string;
  descripcion_contexto?: string;
  objetivo_escena?: string;
  conflicto_escena?: string;
  resultado_escena?: string;
  punto_de_vista?: DocumentReference;
  pov_character_goal?: string;
  pov_character_internal_conflict_scene?: string;
  characters_involved?: DocumentReference[];
  location_id?: DocumentReference;
  plot_threads?: DocumentReference[];
  importance?: number;
  tension?: number;
  tipo_escena?: string;
  emotional_value_start?: string;
  emotional_value_end?: string;
  setting_mood?: string;
  status?: string;
  word_count?: number;
  revision_notes?: string;
  beats?: SceneBeat[];
  timeline_id_ref?: DocumentReference;
  is_ending_scene?: boolean;
  choices?: SceneChoice[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface SceneBeat {
  description: string;
  type: string;
}

export interface SceneChoice {
  text: string;
  next_scene_id: DocumentReference;
  conditions_to_display?: any[];
}

export type SceneWithoutId = Omit<Scene, 'id' | 'createdAt' | 'updatedAt'>;

// Location (Subcollection of Project)
export interface Location {
  id: string;
  name: string;
  alias?: string[];
  image_url?: string;
  type?: string;
  ubicacion_general?: string;
  latitud_longitud?: string;
  description?: string;
  sensory_details?: {
    smell?: string;
    sound?: string;
    sight?: string;
    taste?: string;
    touch?: string;
    emotional_atmosphere?: string;
  };
  importance_to_story?: string;
  notas_adicionales?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type LocationWithoutId = Omit<Location, 'id' | 'createdAt' | 'updatedAt'>;

// Plot Thread (Subcollection of Project)
export interface PlotThread {
  id: string;
  name: string;
  description?: string;
  perspective_character_ref?: DocumentReference;
  status?: string;
  theme_contribution?: string;
  climax_scene_ref?: DocumentReference;
  scenes_ordered?: DocumentReference[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type PlotThreadWithoutId = Omit<PlotThread, 'id' | 'createdAt' | 'updatedAt'>;

// Worldbuilding Element (Subcollection of Project)
export interface WorldbuildingElement {
  id: string;
  name: string;
  type: string;
  description?: string;
  rules_lore?: string;
  image_url?: string;
  related_scenes?: DocumentReference[];
  related_characters?: DocumentReference[];
  notas_adicionales?: string;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type WorldbuildingElementWithoutId = Omit<WorldbuildingElement, 'id' | 'createdAt' | 'updatedAt'>;

// Research Note (Subcollection of Project)
export interface ResearchNote {
  id: string;
  title: string;
  content?: string;
  tags?: string[];
  source_url?: string;
  image_urls?: string[];
  related_to?: RelatedItem[];
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export interface RelatedItem {
  item_ref: DocumentReference;
  item_type: string;
}

export type ResearchNoteWithoutId = Omit<ResearchNote, 'id' | 'createdAt' | 'updatedAt'>;

// Timeline (Subcollection of Project)
export interface Timeline {
  id: string;
  name: string;
  description?: string;
  start_event_description?: string;
  divergence_point_scene_ref?: DocumentReference;
  createdAt: Timestamp;
  updatedAt: Timestamp;
}

export type TimelineWithoutId = Omit<Timeline, 'id' | 'createdAt' | 'updatedAt'>;
