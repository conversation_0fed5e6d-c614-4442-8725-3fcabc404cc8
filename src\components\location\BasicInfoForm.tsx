import { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { X, Plus } from 'lucide-react';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import LocationImageUploader from './LocationImageUploader';

interface BasicInfoFormProps {
  location: {
    name: string;
    alias: string[];
    image_url: string;
    type: string;
    ubicacion_general: string;
    latitud_longitud: string;
  };
  onInputChange: (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => void;
  onAliasChange: (aliases: string[]) => void;
  onFileChange: (url: string) => void;
}

const locationTypes = [
  'Ciudad',
  'Pueblo',
  'Bosque',
  'Montaña',
  'Río',
  'Lago',
  'Océano',
  'Edificio',
  'Casa',
  'Castillo',
  'Planeta',
  'Galaxia',
  'Dimensi<PERSON>',
  'Otro'
];

const BasicInfoForm = ({
  location,
  onInputChange,
  onAliasChange,
  onFileChange
}: BasicInfoFormProps) => {
  const [newAlias, setNewAlias] = useState('');
  const [customType, setCustomType] = useState('');

  const handleAddAlias = () => {
    if (newAlias.trim() !== '' && !location.alias.includes(newAlias.trim())) {
      onAliasChange([...location.alias, newAlias.trim()]);
      setNewAlias('');
    }
  };

  const handleRemoveAlias = (aliasToRemove: string) => {
    onAliasChange(location.alias.filter(alias => alias !== aliasToRemove));
  };

  const handleTypeChange = (value: string) => {
    if (value === 'custom') {
      // If custom is selected, don't update the type yet
      setCustomType('');
    } else {
      // Otherwise update the type with the selected value
      const event = {
        target: {
          name: 'type',
          value
        }
      } as React.ChangeEvent<HTMLSelectElement>;
      onInputChange(event);
    }
  };

  const handleCustomTypeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCustomType(e.target.value);
  };

  const handleCustomTypeBlur = () => {
    if (customType.trim() !== '') {
      const event = {
        target: {
          name: 'type',
          value: customType.trim()
        }
      } as React.ChangeEvent<HTMLInputElement>;
      onInputChange(event);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddAlias();
    }
  };

  // La carga de imágenes ahora se maneja en el componente LocationImageUploader

  return (
    <div className="space-y-4">
      {/* Image Upload */}
      <LocationImageUploader
        name={location.name}
        imageUrl={location.image_url}
        onFileChange={onFileChange}
      />

      {/* Name */}
      <div>
        <Label htmlFor="name">Nombre *</Label>
        <Input
          id="name"
          name="name"
          value={location.name}
          onChange={onInputChange}
          placeholder="Nombre de la localización"
          required
        />
      </div>

      {/* Type */}
      <div>
        <Label htmlFor="type">Tipo</Label>
        <Select
          value={locationTypes.includes(location.type) ? location.type : 'custom'}
          onValueChange={handleTypeChange}
        >
          <SelectTrigger id="type">
            <SelectValue placeholder="Selecciona un tipo" />
          </SelectTrigger>
          <SelectContent>
            {locationTypes.map(type => (
              <SelectItem key={type} value={type}>{type}</SelectItem>
            ))}
            <SelectItem value="custom">Otro (personalizado)</SelectItem>
          </SelectContent>
        </Select>

        {(!locationTypes.includes(location.type) || customType) && (
          <Input
            className="mt-2"
            placeholder="Tipo personalizado"
            value={customType || location.type}
            onChange={handleCustomTypeChange}
            onBlur={handleCustomTypeBlur}
          />
        )}
      </div>

      {/* Ubicación General */}
      <div>
        <Label htmlFor="ubicacion_general">Ubicación General</Label>
        <Input
          id="ubicacion_general"
          name="ubicacion_general"
          value={location.ubicacion_general}
          onChange={onInputChange}
          placeholder="Ej: Continente Norte, Sistema Solar X"
        />
      </div>

      {/* Coordenadas */}
      <div>
        <Label htmlFor="latitud_longitud">Coordenadas (opcional)</Label>
        <Input
          id="latitud_longitud"
          name="latitud_longitud"
          value={location.latitud_longitud}
          onChange={onInputChange}
          placeholder="Latitud/Longitud o coordenadas ficticias"
        />
      </div>

      {/* Aliases */}
      <div>
        <Label htmlFor="alias">Nombres alternativos</Label>
        <div className="flex gap-2">
          <Input
            id="alias"
            value={newAlias}
            onChange={(e) => setNewAlias(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Añadir nombre alternativo"
          />
          <Button type="button" onClick={handleAddAlias} variant="outline">
            <Plus className="h-4 w-4" />
          </Button>
        </div>

        {location.alias.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {location.alias.map((alias, index) => (
              <div
                key={index}
                className="bg-secondary text-secondary-foreground px-2 py-1 rounded-md flex items-center text-sm"
              >
                {alias}
                <Button
                  variant="ghost"
                  size="icon"
                  className="h-5 w-5 ml-1"
                  onClick={() => handleRemoveAlias(alias)}
                >
                  <X className="h-3 w-3" />
                </Button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default BasicInfoForm;
