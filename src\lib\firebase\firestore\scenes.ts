import { 
  collection, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  doc, 
  getDocs, 
  getDoc,
  query, 
  where,
  orderBy,
  serverTimestamp,
  DocumentReference,
  writeBatch
} from "firebase/firestore";
import { db } from "../config";
import { Scene, SceneWithoutId } from "@/types/firestore-models";

/**
 * Get the scenes subcollection for a project
 */
export const getScenesCollection = (projectId: string) => {
  return collection(db, "projects", projectId, "scenes");
};

/**
 * Create a new scene in a project
 */
export const createScene = async (
  projectId: string, 
  sceneData: SceneWithoutId
): Promise<Scene> => {
  try {
    const scenesCollection = getScenesCollection(projectId);
    
    const docRef = await addDoc(scenesCollection, {
      ...sceneData,
      createdAt: serverTimestamp(),
      updatedAt: serverTimestamp()
    });
    
    return { 
      id: docRef.id, 
      ...sceneData,
      createdAt: serverTimestamp() as any,
      updatedAt: serverTimestamp() as any
    };
  } catch (error) {
    console.error("Error creating scene:", error);
    throw error;
  }
};

/**
 * Get all scenes for a project
 */
export const getProjectScenes = async (projectId: string): Promise<Scene[]> => {
  try {
    const scenesCollection = getScenesCollection(projectId);
    const querySnapshot = await getDocs(scenesCollection);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Scene[];
  } catch (error) {
    console.error("Error fetching project scenes:", error);
    throw error;
  }
};

/**
 * Get all scenes for a chapter, ordered by their global_order
 */
export const getChapterScenes = async (
  projectId: string, 
  chapterRef: DocumentReference
): Promise<Scene[]> => {
  try {
    const scenesCollection = getScenesCollection(projectId);
    const q = query(
      scenesCollection, 
      where("chapter_id", "==", chapterRef),
      orderBy("global_order", "asc")
    );
    const querySnapshot = await getDocs(q);
    
    return querySnapshot.docs.map(doc => ({
      id: doc.id,
      ...doc.data()
    })) as Scene[];
  } catch (error) {
    console.error("Error fetching chapter scenes:", error);
    throw error;
  }
};

/**
 * Get a scene by ID
 */
export const getSceneById = async (projectId: string, sceneId: string): Promise<Scene> => {
  try {
    const sceneRef = doc(db, "projects", projectId, "scenes", sceneId);
    const sceneSnap = await getDoc(sceneRef);
    
    if (sceneSnap.exists()) {
      return { 
        id: sceneSnap.id, 
        ...sceneSnap.data() 
      } as Scene;
    } else {
      throw new Error("Scene not found");
    }
  } catch (error) {
    console.error("Error fetching scene:", error);
    throw error;
  }
};

/**
 * Update a scene
 */
export const updateScene = async (
  projectId: string,
  sceneId: string, 
  sceneData: Partial<Scene>
): Promise<Partial<Scene>> => {
  try {
    const sceneRef = doc(db, "projects", projectId, "scenes", sceneId);
    
    const updateData = {
      ...sceneData,
      updatedAt: serverTimestamp()
    };
    
    await updateDoc(sceneRef, updateData);
    
    return { 
      id: sceneId, 
      ...sceneData 
    };
  } catch (error) {
    console.error("Error updating scene:", error);
    throw error;
  }
};

/**
 * Delete a scene
 */
export const deleteScene = async (projectId: string, sceneId: string): Promise<{ success: boolean }> => {
  try {
    const sceneRef = doc(db, "projects", projectId, "scenes", sceneId);
    await deleteDoc(sceneRef);
    
    return { success: true };
  } catch (error) {
    console.error("Error deleting scene:", error);
    throw error;
  }
};

/**
 * Delete all scenes for a project (used when deleting a project)
 */
export const deleteAllProjectScenes = async (projectId: string): Promise<void> => {
  try {
    const scenesCollection = getScenesCollection(projectId);
    const querySnapshot = await getDocs(scenesCollection);
    
    const batch = writeBatch(db);
    
    querySnapshot.docs.forEach(sceneDoc => {
      const sceneRef = doc(db, "projects", projectId, "scenes", sceneDoc.id);
      batch.delete(sceneRef);
    });
    
    await batch.commit();
  } catch (error) {
    console.error("Error deleting all project scenes:", error);
    throw error;
  }
};

/**
 * Get a reference to a scene document
 */
export const getSceneRef = (projectId: string, sceneId: string): DocumentReference => {
  return doc(db, "projects", projectId, "scenes", sceneId);
};
